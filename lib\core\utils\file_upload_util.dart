/// -----
/// file_upload_util.dart
/// 
/// 通用文件上传工具类
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dio/dio.dart';
import '../error/exceptions/server_exception.dart';
import '../network/dio_client.dart';

/// 文件上传类型枚举
enum FileUploadType {
  /// 一般文件上传 (type=10)
  general('10'),
  /// PDF文件上传 (type=13)
  pdf('13');

  const FileUploadType(this.value);
  final String value;
}

/// 文件上传结果
class FileUploadResult {
  /// 文件名
  final String fileName;
  
  /// 文件URL
  final String fileUrl;

  const FileUploadResult({
    required this.fileName,
    required this.fileUrl,
  });

  /// 从响应数据创建结果
  factory FileUploadResult.fromResponse(Map<String, dynamic> response) {
    return FileUploadResult(
      fileName: response['fileName'] as String? ?? '',
      fileUrl: response['fileUrl'] as String? ?? '',
    );
  }
}

/// 通用文件上传工具类
class FileUploadUtil {
  final DioClient _dioClient;

  FileUploadUtil(this._dioClient);

  /// 上传文件到服务器
  ///
  /// [filePath] 文件路径
  /// [fileName] 文件名
  /// [uploadType] 上传类型
  /// [onProgress] 上传进度回调
  /// 返回：FileUploadResult 包含fileName和fileUrl的上传结果
  /// 抛出：ServerException 服务器异常
  Future<FileUploadResult> uploadFile({
    required String filePath,
    required String fileName,
    required FileUploadType uploadType,
    Function(double progress)? onProgress,
  }) async {
    try {
      // 创建FormData
      final formData = FormData.fromMap({
        'type': uploadType.value,
        'file': await MultipartFile.fromFile(
          filePath,
          filename: fileName,
        ),
      });

      final response = await _dioClient.post(
        'userservice/v1/common/file/upload',
        data: formData,
        onSendProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            final progress = sent / total;
            onProgress(progress);
          }
        },
      );

      // DioClient已经处理了响应格式，直接返回data字段
      if (response is Map<String, dynamic>) {
        final result = FileUploadResult.fromResponse(response);
        
        if (result.fileUrl.isEmpty) {
          throw ServerException('文件上传成功但未返回文件URL');
        }

        return result;
      } else {
        throw ServerException('文件上传响应格式错误');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('文件上传失败: $e');
    }
  }

  /// 上传PDF文件（便捷方法）
  Future<FileUploadResult> uploadPdfFile({
    required String filePath,
    required String fileName,
    Function(double progress)? onProgress,
  }) async {
    // 验证文件是否为PDF格式
    if (!isPdfFile(fileName)) {
      throw ServerException('只支持PDF文件格式');
    }

    return uploadFile(
      filePath: filePath,
      fileName: fileName,
      uploadType: FileUploadType.pdf,
      onProgress: onProgress,
    );
  }

  /// 上传一般文件（便捷方法）
  Future<FileUploadResult> uploadGeneralFile({
    required String filePath,
    required String fileName,
    Function(double progress)? onProgress,
  }) async {
    return uploadFile(
      filePath: filePath,
      fileName: fileName,
      uploadType: FileUploadType.general,
      onProgress: onProgress,
    );
  }

  /// 验证文件是否为PDF格式
  static bool isPdfFile(String fileName) {
    return fileName.toLowerCase().endsWith('.pdf');
  }

  /// 获取文件大小限制（10MB）
  static int get maxFileSize => 10 * 1024 * 1024;

  /// 验证文件大小
  static bool isValidFileSize(int fileSize) {
    return fileSize <= maxFileSize;
  }

  /// 格式化文件大小显示
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }
}
