/// -----
/// report_write_bloc.dart
/// 
/// 写报告页面的BLoC状态管理
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/services/file_picker_service.dart';
import 'package:flutter_demo/core/utils/file_upload_util.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/domain/usecases/get_report_tip.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_state.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';

class ReportWriteBloc extends Bloc<ReportWriteEvent, ReportWriteState> {
  final GetReportTip _getReportTip;
  final FileUploadUtil _fileUploadUtil;

  ReportWriteBloc(this._getReportTip, this._fileUploadUtil) : super(ReportWriteInitial()) {
    on<InitializeReportWriteEvent>(_onInitializeReportWrite);
    on<SelectCourseEvent>(_onSelectCourse);
    on<UpdateReportContentEvent>(_onUpdateReportContent);
    on<GenerateAIContentEvent>(_onGenerateAIContent);
    on<GetReportTipEvent>(_onGetReportTip);
    on<SelectFileEvent>(_onSelectFile);
    on<AddAttachmentEvent>(_onAddAttachment);
    on<RemoveAttachmentEvent>(_onRemoveAttachment);
    on<UploadAttachmentEvent>(_onUploadAttachment);
    on<SaveDraftEvent>(_onSaveDraft);
    on<SubmitReportEvent>(_onSubmitReport);
    on<LoadHistoryReportsEvent>(_onLoadHistoryReports);
  }

  /// 初始化写报告页面
  Future<void> _onInitializeReportWrite(
    InitializeReportWriteEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    emit(ReportWriteLoading());

    try {
      // 获取全局实习计划状态
      final planListBloc = GetIt.instance<PlanListGlobalBloc>();
      final planState = planListBloc.state;

      String selectedCourseId = '1';
      String selectedCourseName = '暂无实习计划';
      List<String> availableCourses = [];

      if (planState is PlanListGlobalLoadedState) {
        if (planState.plans.isNotEmpty) {
          final currentPlan = planState.currentPlan ?? planState.plans.first;
          selectedCourseId = currentPlan.planId;
          selectedCourseName = currentPlan.displayName;
          availableCourses = planState.plans.map((plan) => plan.displayName).toList();

          // 获取报告提示词（仅对日报和周报）
          if (event.reportType == ReportType.daily || event.reportType == ReportType.weekly) {
            add(GetReportTipEvent(
              planId: selectedCourseId,
              reportType: event.reportType,
            ));
          }
        }
      }

      String initialContent = '';
      bool hasAIContent = false;

      // 根据报告类型设置初始内容
      if (event.reportType != ReportType.daily) {
        // 非日报类型，生成AI内容
        initialContent = _generateAIContent(event.reportType);
        hasAIContent = true;
      }

      emit(ReportWriteLoaded(
        reportType: event.reportType,
        reportId: event.reportId,
        selectedCourseId: selectedCourseId,
        selectedCourseName: selectedCourseName,
        availableCourses: availableCourses,
        content: initialContent,
        attachments: const [],
        hasAIContent: hasAIContent,
      ));
    } catch (e) {
      emit(ReportWriteError('初始化失败: ${e.toString()}'));
    }
  }

  /// 选择课程
  Future<void> _onSelectCourse(
    SelectCourseEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      emit(currentState.copyWith(
        selectedCourseId: event.courseId,
        selectedCourseName: event.courseName,
      ));
    }
  }

  /// 更新报告内容
  Future<void> _onUpdateReportContent(
    UpdateReportContentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      emit(currentState.copyWith(content: event.content));
    }
  }

  /// 生成AI内容
  Future<void> _onGenerateAIContent(
    GenerateAIContentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      
      emit(ReportWriteGeneratingAI(
        reportType: currentState.reportType,
        reportId: currentState.reportId,
        selectedCourseId: currentState.selectedCourseId,
        selectedCourseName: currentState.selectedCourseName,
        availableCourses: currentState.availableCourses,
        content: currentState.content,
        attachments: currentState.attachments,
        hasAIContent: currentState.hasAIContent,
        isDraft: currentState.isDraft,
      ));

      try {
        // 模拟AI生成内容
        await Future.delayed(const Duration(seconds: 2));
        
        final aiContent = _generateAIContent(event.reportType);
        
        emit(currentState.copyWith(
          content: aiContent,
          hasAIContent: true,
        ));
      } catch (e) {
        emit(ReportWriteError('AI内容生成失败: ${e.toString()}'));
      }
    }
  }

  /// 获取报告提示词
  Future<void> _onGetReportTip(
    GetReportTipEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      try {
        // 根据报告类型确定type参数
        int type = 1; // 默认日报
        if (event.reportType == ReportType.weekly) {
          type = 2; // 周报
        }

        final result = await _getReportTip(GetReportTipParams(
          planId: event.planId,
          type: type,
        ));

        result.fold(
          (failure) {
            emit(ReportWriteError('获取报告提示词失败: ${failure.message}'));
          },
          (reportTip) {
            emit(currentState.copyWith(
              reportTipQuestions: reportTip.questions,
            ));
          },
        );
      } catch (e) {
        emit(ReportWriteError('获取报告提示词失败: ${e.toString()}'));
      }
    }
  }

  /// 选择文件
  Future<void> _onSelectFile(
    SelectFileEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      // 检查是否已达到文件上传限制
      if (currentState.attachments.length >= 1) {
        emit(ReportWriteError('最多只能上传1个附件'));
        return;
      }

      try {
        // 这里可以实现文件选择逻辑
        // 由于文件选择在UI层处理，这里主要用于状态管理
        // 实际的文件选择会通过AddAttachmentEvent添加
      } catch (e) {
        emit(ReportWriteError('文件选择失败: ${e.toString()}'));
      }
    }
  }

  /// 添加附件
  Future<void> _onAddAttachment(
    AddAttachmentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      // 检查文件数量限制
      if (currentState.attachments.isNotEmpty) {
        emit(const ReportWriteError('最多只能上传1个附件'));
        return;
      }

      // 验证文件是否为PDF格式
      if (!FileUploadUtil.isPdfFile(event.fileName)) {
        emit(const ReportWriteError('只支持PDF文件格式'));
        return;
      }

      final newAttachment = AttachmentModel(
        filePath: event.filePath,
        fileName: event.fileName,
        fileSize: event.fileSize,
      );

      final updatedAttachments = [...currentState.attachments, newAttachment];
      emit(currentState.copyWith(attachments: updatedAttachments));

      // 自动开始上传
      add(UploadAttachmentEvent(
        filePath: event.filePath,
        fileName: event.fileName,
      ));
    }
  }

  /// 上传附件到服务器
  Future<void> _onUploadAttachment(
    UploadAttachmentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      // 发出上传中状态
      emit(ReportWriteUploadingAttachment(
        reportType: currentState.reportType,
        reportId: currentState.reportId,
        selectedCourseId: currentState.selectedCourseId,
        selectedCourseName: currentState.selectedCourseName,
        availableCourses: currentState.availableCourses,
        content: currentState.content,
        attachments: currentState.attachments,
        hasAIContent: currentState.hasAIContent,
        isSubmitting: currentState.isSubmitting,
        isDraft: currentState.isDraft,
        uploadingFileName: event.fileName,
        uploadProgress: 0.0,
      ));

      try {
        // 使用文件上传工具上传PDF文件
        final uploadResult = await _fileUploadUtil.uploadPdfFile(
          filePath: event.filePath,
          fileName: event.fileName,
          onProgress: (progress) {
            if (state is ReportWriteUploadingAttachment) {
              emit(ReportWriteUploadingAttachment(
                reportType: currentState.reportType,
                reportId: currentState.reportId,
                selectedCourseId: currentState.selectedCourseId,
                selectedCourseName: currentState.selectedCourseName,
                availableCourses: currentState.availableCourses,
                content: currentState.content,
                attachments: currentState.attachments,
                hasAIContent: currentState.hasAIContent,
                isSubmitting: currentState.isSubmitting,
                isDraft: currentState.isDraft,
                uploadingFileName: event.fileName,
                uploadProgress: progress,
              ));
            }
          },
        );

        // 上传成功，更新附件状态和保存URL
        final updatedAttachments = currentState.attachments.map((attachment) {
          if (attachment.filePath == event.filePath) {
            return attachment.copyWith(
              isUploaded: true,
              serverUrl: uploadResult.fileUrl,
            );
          }
          return attachment;
        }).toList();

        // 保存上传的文件URL
        final uploadedUrls = <String>[...(currentState.uploadedFileUrls ?? []), uploadResult.fileUrl];

        emit(currentState.copyWith(
          attachments: updatedAttachments,
          uploadedFileUrls: uploadedUrls,
        ));

        emit(ReportWriteAttachmentUploadSuccess(
          message: '附件上传成功',
          attachment: updatedAttachments.firstWhere(
            (a) => a.filePath == event.filePath,
          ),
        ));

        // 返回到正常状态
        emit(currentState.copyWith(
          attachments: updatedAttachments,
          uploadedFileUrls: uploadedUrls,
        ));

      } on Exception catch (e) {
        emit(ReportWriteAttachmentUploadError('附件上传失败: ${e.toString()}'));
        // 返回到之前的状态
        emit(currentState);
      }
    }
  }

  /// 删除附件
  Future<void> _onRemoveAttachment(
    RemoveAttachmentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      final updatedAttachments = [...currentState.attachments];
      if (event.index >= 0 && event.index < updatedAttachments.length) {
        updatedAttachments.removeAt(event.index);
        emit(currentState.copyWith(attachments: updatedAttachments));
      }
    }
  }

  /// 保存草稿
  Future<void> _onSaveDraft(
    SaveDraftEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      
      try {
        // 模拟保存草稿
        await Future.delayed(const Duration(seconds: 1));
        
        emit(ReportWriteSaveDraftSuccess('草稿保存成功'));
        
        // 返回到加载状态，标记为草稿
        emit(currentState.copyWith(isDraft: true));
      } catch (e) {
        emit(ReportWriteError('保存草稿失败: ${e.toString()}'));
      }
    }
  }

  /// 提交报告
  Future<void> _onSubmitReport(
    SubmitReportEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      // 验证内容
      if (currentState.content.trim().isEmpty) {
        emit(ReportWriteError('请填写报告内容'));
        return;
      }

      emit(ReportWriteSubmitting(
        reportType: currentState.reportType,
        reportId: currentState.reportId,
        selectedCourseId: currentState.selectedCourseId,
        selectedCourseName: currentState.selectedCourseName,
        availableCourses: currentState.availableCourses,
        content: currentState.content,
        attachments: currentState.attachments,
        hasAIContent: currentState.hasAIContent,
        isDraft: currentState.isDraft,
      ));

      try {
        // 调用真实的报告提交接口
        await _submitReportToServer(currentState);

        emit(ReportWriteSubmitSuccess('报告提交成功'));
      } catch (e) {
        emit(ReportWriteError('提交失败: ${e.toString()}'));
      }
    }
  }

  /// 提交报告到服务器
  Future<void> _submitReportToServer(ReportWriteLoaded state) async {
    // 获取当前实习计划ID
    final localStorage = GetIt.instance<LocalStorage>();
    final planId = localStorage.getString('current_plan_id') ?? '2';

    // 获取报告附件URL（如果有的话）
    String? reportFileUrl;
    if (state.uploadedFileUrls != null && state.uploadedFileUrls!.isNotEmpty) {
      reportFileUrl = state.uploadedFileUrls!.first;
    }

    // 计算报告日期
    final now = DateTime.now();
    final reportDate = _calculateReportDate(state.reportType, now);
    final reportStartDate = _calculateReportStartDate(state.reportType, now);
    final reportEndDate = _calculateReportEndDate(state.reportType, now);

    // 构建请求参数
    final requestData = {
      'planId': int.parse(planId),
      'reportContent': state.content.trim(),
      'reportDate': reportDate.millisecondsSinceEpoch,
      'reportStartDate': reportStartDate.millisecondsSinceEpoch,
      'reportEndDate': reportEndDate.millisecondsSinceEpoch,
      'reportFile': reportFileUrl ?? '', // 如果没有上传PDF附件，这个字段为空
      'reportType': _getReportTypeValue(state.reportType),
    };

    // 调用API
    final dioClient = GetIt.instance<DioClient>();
    await dioClient.post(
      'internshipservice/v1/internship/report/student/save',
      data: requestData,
    );
  }

  /// 获取报告类型对应的数值
  int _getReportTypeValue(ReportType reportType) {
    switch (reportType) {
      case ReportType.daily:
        return 1;
      case ReportType.weekly:
        return 2;
      case ReportType.monthly:
        return 3;
      case ReportType.summary:
        return 4;
    }
  }

  /// 计算报告日期
  DateTime _calculateReportDate(ReportType reportType, DateTime now) {
    switch (reportType) {
      case ReportType.daily:
        return now; // 日报为当前日期
      case ReportType.weekly:
        return _getWeekStart(now); // 周报为本周开始日期
      case ReportType.monthly:
        return DateTime(now.year, now.month); // 月报为本月1号
      case ReportType.summary:
        return now; // 总结报告为当前日期
    }
  }

  /// 计算报告开始日期
  DateTime _calculateReportStartDate(ReportType reportType, DateTime now) {
    switch (reportType) {
      case ReportType.daily:
        return now; // 日报开始和结束都是当天
      case ReportType.weekly:
        return _getWeekStart(now); // 周报开始日期为本周开始
      case ReportType.monthly:
        return DateTime(now.year, now.month); // 月报开始日期为本月1号
      case ReportType.summary:
        return now; // 总结报告开始日期为当前日期
    }
  }

  /// 计算报告结束日期
  DateTime _calculateReportEndDate(ReportType reportType, DateTime now) {
    switch (reportType) {
      case ReportType.daily:
        return now; // 日报开始和结束都是当天
      case ReportType.weekly:
        return _getWeekEnd(now); // 周报结束日期为本周结束
      case ReportType.monthly:
        return DateTime(now.year, now.month + 1, 0); // 月报结束日期为本月最后一天
      case ReportType.summary:
        return now; // 总结报告结束日期为当前日期
    }
  }

  /// 获取本周开始日期（周一）
  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  /// 获取本周结束日期（周日）
  DateTime _getWeekEnd(DateTime date) {
    final weekday = date.weekday;
    return date.add(Duration(days: 7 - weekday));
  }

  /// 加载历史报告
  Future<void> _onLoadHistoryReports(
    LoadHistoryReportsEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    try {
      // 模拟加载历史报告
      await Future.delayed(const Duration(seconds: 1));
      
      // 这里应该调用仓库层获取历史报告
      emit(ReportWriteHistoryLoaded([]));
    } catch (e) {
      emit(ReportWriteError('加载历史报告失败: ${e.toString()}'));
    }
  }

  /// 生成AI内容（模拟）
  String _generateAIContent(ReportType reportType) {
    switch (reportType) {
      case ReportType.weekly:
        return '本周完成的任务主要有学生端的签到，成绩查看等，工作中应该积极思考整个业务流程，我觉得部分功能需求还需要进一步研究讨论，经过上周的实习，我对我所负责的业务了解更深入的了解！';
      case ReportType.monthly:
        return '本月主要完成了实习管理系统的核心功能开发，包括学生签到、成绩管理、报告提交等模块。通过这个月的实习，我对软件开发流程有了更深入的理解，技术能力得到了显著提升。';
      case ReportType.summary:
        return '通过这次实习，我深刻体会到了理论与实践相结合的重要性。在实际工作中，不仅要掌握扎实的专业知识，还要具备良好的沟通协调能力和团队合作精神。这次实习为我今后的职业发展奠定了坚实的基础。';
      default:
        return '';
    }
  }
}
