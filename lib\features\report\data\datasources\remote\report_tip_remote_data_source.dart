/// -----
/// report_tip_remote_data_source.dart
/// 
/// 报告提示词远程数据源
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../../../core/error/exceptions/server_exception.dart';
import '../../../../../core/network/dio_client.dart';
import '../../models/report_tip_model.dart';

/// 报告提示词远程数据源接口
abstract class ReportTipRemoteDataSource {
  /// 获取报告提示词
  ///
  /// [planId] 实习计划ID
  /// [type] 类型（1=日报, 2=周报）
  /// 返回：ReportTipModel 报告提示词模型
  /// 抛出：ServerException 服务器异常
  Future<ReportTipModel> getReportTip({
    required String planId,
    required int type,
  });
}

/// 报告提示词远程数据源实现
class ReportTipRemoteDataSourceImpl implements ReportTipRemoteDataSource {
  final DioClient _dioClient;

  ReportTipRemoteDataSourceImpl(this._dioClient);

  @override
  Future<ReportTipModel> getReportTip({
    required String planId,
    required int type,
  }) async {
    try {
      final response = await _dioClient.get(
        'internshipservice/v1/internship/report/query/tip',
        queryParameters: {
          'planId': planId,
          'type': type,
        },
      );

      // DioClient已经处理了响应格式，直接返回data字段
      if (response is Map<String, dynamic>) {
        return ReportTipModel.fromJson(response);
      } else {
        throw ServerException('获取报告提示词响应格式错误');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取报告提示词失败: $e');
    }
  }
}
