/// -----
/// internship_approval_request_model.dart
///
/// 实习申请审批请求数据模型类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/internship_approval_request.dart';

/// 实习申请审批请求数据模型类
/// 
/// 继承自实体类，添加JSON序列化功能
class InternshipApprovalRequestModel extends InternshipApprovalRequest {
  const InternshipApprovalRequestModel({
    required super.id,
    required super.reviewOpinion,
    required super.status,
  });

  /// 从实体类创建模型实例
  factory InternshipApprovalRequestModel.fromEntity(InternshipApprovalRequest entity) {
    return InternshipApprovalRequestModel(
      id: entity.id,
      reviewOpinion: entity.reviewOpinion,
      status: entity.status,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'remark': reviewOpinion,  // 使用接口文档要求的参数名
      'status': status,
    };
  }

  /// 从JSON创建模型实例
  factory InternshipApprovalRequestModel.fromJson(Map<String, dynamic> json) {
    return InternshipApprovalRequestModel(
      id: json['id'] ?? '',
      reviewOpinion: json['reviewOpinion'] ?? '',
      status: json['status'] ?? 0,
    );
  }

  /// 转换为实体类
  InternshipApprovalRequest toEntity() {
    return InternshipApprovalRequest(
      id: id,
      reviewOpinion: reviewOpinion,
      status: status,
    );
  }
}
