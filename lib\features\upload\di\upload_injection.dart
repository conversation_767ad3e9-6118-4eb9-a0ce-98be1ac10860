/// -----
/// upload_injection.dart
/// 
/// 文件上传模块依赖注入
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/utils/file_upload_util.dart';
import '../../../core/network/network_info.dart';
import '../data/datasources/remote/file_upload_remote_data_source.dart';
import '../data/repositories/file_upload_repository_impl.dart';
import '../domain/repositories/file_upload_repository.dart';
import '../domain/usecases/get_file_requirements_usecase.dart';
import '../domain/usecases/upload_file_usecase.dart';
import '../domain/usecases/get_file_detail_usecase.dart';
import '../presentation/bloc/file_upload_bloc.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化文件上传模块依赖
/// 
/// 注册文件上传模块的数据源、仓库、用例和BLoC
Future<void> setupUploadDependencies() async {
  // 数据源
  getIt.registerLazySingleton<FileUploadRemoteDataSource>(
    () => FileUploadRemoteDataSourceImpl(getIt<DioClient>(), getIt<FileUploadUtil>()),
  );

  // 仓库
  getIt.registerLazySingleton<FileUploadRepository>(
    () => FileUploadRepositoryImpl(
      getIt<FileUploadRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // 用例
  getIt.registerFactory<GetFileRequirementsUseCase>(
    () => GetFileRequirementsUseCase(getIt<FileUploadRepository>()),
  );

  getIt.registerFactory<UploadFileUseCase>(
    () => UploadFileUseCase(getIt<FileUploadRepository>()),
  );

  getIt.registerFactory<GetFileDetailUseCase>(
    () => GetFileDetailUseCase(getIt<FileUploadRepository>()),
  );

  // BLoC
  getIt.registerFactory<FileUploadBloc>(
    () => FileUploadBloc(
      getIt<GetFileRequirementsUseCase>(),
      getIt<UploadFileUseCase>(),
      getIt<GetFileDetailUseCase>(),
    ),
  );
}
