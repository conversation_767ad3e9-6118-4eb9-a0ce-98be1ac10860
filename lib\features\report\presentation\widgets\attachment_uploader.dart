/// -----
/// attachment_uploader.dart
///
/// 附件上传组件，支持文件选择、上传进度显示和文件管理
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_state.dart';

class AttachmentUploader extends StatefulWidget {
  final List<AttachmentModel> attachments;
  final Function(String filePath, String fileName, String? fileSize)? onFileSelected;
  final Function(int)? onRemoveFile;
  final bool isUploading;
  final double uploadProgress;
  final String? uploadingFileName;
  final int maxFiles;

  const AttachmentUploader({
    Key? key,
    required this.attachments,
    this.onFileSelected,
    this.onRemoveFile,
    this.isUploading = false,
    this.uploadProgress = 0.0,
    this.uploadingFileName,
    this.maxFiles = 1,
  }) : super(key: key);

  @override
  State<AttachmentUploader> createState() => _AttachmentUploaderState();
}

class _AttachmentUploaderState extends State<AttachmentUploader> {

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 25.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 附件列表
          if (widget.attachments.isNotEmpty) ...[
            _buildAttachmentList(),
            SizedBox(height: 16.h),
          ],

          // 上传按钮
          if (widget.attachments.length < widget.maxFiles)
            _buildUploadButton(),

          // 上传进度
          if (widget.isUploading)
            _buildUploadProgress(),
        ],
      ),
    );
  }

  Widget _buildAttachmentList() {
    return Column(
      children: widget.attachments.asMap().entries.map((entry) {
        final index = entry.key;
        final attachment = entry.value;
        return _buildAttachmentItem(attachment, index);
      }).toList(),
    );
  }

  Widget _buildAttachmentItem(AttachmentModel attachment, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          // 文件图标
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: AppTheme.blue2165f6.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              _getFileIcon(attachment.fileName),
              color: AppTheme.blue2165f6,
              size: 24.sp,
            ),
          ),

          SizedBox(width: 12.w),

          // 文件信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attachment.fileName,
                  style: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (attachment.fileSize != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    attachment.fileSize!,
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),

          // 状态指示器
          if (attachment.isUploaded)
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 24.sp,
            )
          else
            Icon(
              Icons.cloud_upload_outlined,
              color: Colors.grey[400],
              size: 24.sp,
            ),

          SizedBox(width: 8.w),

          // 删除按钮
          GestureDetector(
            onTap: () => widget.onRemoveFile?.call(index),
            child: Container(
              padding: EdgeInsets.all(4.w),
              child: Icon(
                Icons.close,
                color: Colors.grey[600],
                size: 20.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadButton() {
    return GestureDetector(
      onTap: widget.isUploading ? null : _showFilePickerOptions,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 20.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: AppTheme.blue2165f6.withValues(alpha: 0.3),
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_upload_outlined,
              color: AppTheme.blue2165f6,
              size: 32.sp,
            ),
            SizedBox(height: 8.h),
            Text(
              '点击上传附件',
              style: TextStyle(
                fontSize: 28.sp,
                color: AppTheme.blue2165f6,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              '仅支持PDF格式，单个文件不超过10MB',
              style: TextStyle(
                fontSize: 24.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadProgress() {
    return Container(
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.w,
                child: CircularProgressIndicator(
                  value: widget.uploadProgress,
                  strokeWidth: 2,
                  color: AppTheme.blue2165f6,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  '正在上传: ${widget.uploadingFileName ?? "文件"}',
                  style: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '${(widget.uploadProgress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          LinearProgressIndicator(
            value: widget.uploadProgress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.blue2165f6),
          ),
        ],
      ),
    );
  }

  void _showFilePickerOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.only(top: 20.h, bottom: 30.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 20.h),
                child: Text(
                  '选择PDF文件',
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Divider(height: 1, thickness: 0.5, color: Colors.grey[300]),
              _buildFileOption(
                icon: Icons.picture_as_pdf,
                title: '选择PDF文档',
                onTap: () {
                  Navigator.pop(context);
                  _pickPdfDocument();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFileOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppTheme.blue2165f6,
        size: 24.sp,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 28.sp,
          color: Colors.black87,
        ),
      ),
      onTap: onTap,
    );
  }

  Future<void> _pickPdfDocument() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = result.files.single;
        await _handleSelectedFile(file.path!, file.name);
      }
    } on Exception catch (e) {
      _showError('选择PDF文档失败: $e');
    }
  }

  Future<void> _handleSelectedFile(String filePath, String fileName) async {
    try {
      final file = File(filePath);

      // 检查文件大小（限制10MB）
      final fileSize = await file.length();
      if (fileSize > 10 * 1024 * 1024) {
        _showError('文件大小不能超过10MB');
        return;
      }

      // 格式化文件大小
      final formattedSize = _formatFileSize(fileSize);

      // 触发文件选择回调，传递文件信息
      widget.onFileSelected?.call(filePath, fileName, formattedSize);

    } catch (e) {
      _showError('处理文件失败: $e');
    }
  }



  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.insert_drive_file;
    }
  }

  void _showError(String message) {
    if (mounted) {
      AppSnackBar.showError(context, message);
    }
  }
}