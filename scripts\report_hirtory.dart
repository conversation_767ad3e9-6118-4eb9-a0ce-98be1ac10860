import 'dart:convert';
import 'package:http/http.dart' as http;

Future<void> fetchStudentReports({
  required String token,
  int pageNum = 1,
  int pageSize = 10,
  required String planId,
  int type = 0,
}) async {
  final url = Uri.parse('http://**************:8088/internshipservice/v1/internship/report/student/list');

  final headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $token', // 或者根据你的API要求可能是 'Token $token'
  };

  final body = jsonEncode({
    'pageNum': pageNum,
    'pageSize': pageSize,
    'planId': planId,
    'type': type,
  });

  try {
    final response = await http.post(
      url,
      headers: headers,
      body: body,
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      print('请求成功: $responseData');
      // 在这里处理返回的数据
    } else {
      print('请求失败，状态码: ${response.statusCode}');
      print('响应体: ${response.body}');
    }
  } catch (e) {
    print('请求过程中发生错误: $e');
  }
}

// 使用示例
void main() async {
  await fetchStudentReports(
    token: 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDIyOTUyNyIsImRlcHQiOjE5MjcyNDgzOTY5MDAzNTIwMDAsInVzZXJUeXBlIjoxLCJ1c2VyS2V5IjoiNjZFMjI4REU1M0UxMTFGMDg0OEUwMjQyQzBBODY0MDcifQ.lIoiRewEMIg9wcSm253MDbhMZYcwpq8YdNV-rDOF1DUn-K-2fOE0s2hKzvOcqxIeWJqldgjOhvPhLgZ2k5lC0w', // 替换为实际的token
    planId: '2', // 替换为实际的实习计划ID
    pageNum: 1,
    pageSize: 10,
    type: 0, // 0表示日报
  );
}