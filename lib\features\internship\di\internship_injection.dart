/// -----
/// internship_injection.dart
/// 
/// 实习模块依赖注入配置
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/network/network_info.dart';
import '../../../core/storage/local_storage.dart';
import '../data/datasources/remote/internship_plan_remote_data_source.dart';
import '../data/datasources/remote/internship_plan_remote_data_source_impl.dart';
import '../data/datasources/remote/free_internship_exempt_remote_data_source.dart';
import '../data/datasources/remote/free_internship_exempt_remote_data_source_impl.dart';
import '../data/datasources/remote/internship_application_remote_data_source.dart';
import '../data/datasources/remote/internship_application_remote_data_source_impl.dart';
import 'package:flutter_demo/features/internship/data/datasources/remote/internship_approval_remote_data_source.dart';
import 'package:flutter_demo/features/internship/data/datasources/remote/internship_approval_remote_data_source_impl.dart';
import '../data/datasources/remote/internship_approval_detail_remote_data_source.dart';
import '../data/datasources/remote/internship_approval_detail_remote_data_source_impl.dart';
import '../data/repositories/internship_plan_repository_impl.dart';
import '../data/repositories/free_internship_exempt_repository_impl.dart';
import '../data/repositories/internship_application_repository_impl.dart';
import 'package:flutter_demo/features/internship/data/repositories/internship_approval_repository_impl.dart';
import '../data/repositories/internship_approval_detail_repository_impl.dart';
import 'package:flutter_demo/features/internship/domain/repositories/internship_plan_repository.dart';
import 'package:flutter_demo/features/internship/domain/repositories/free_internship_exempt_repository.dart';
import 'package:flutter_demo/features/internship/domain/repositories/internship_application_repository.dart';
import 'package:flutter_demo/features/internship/domain/repositories/internship_approval_repository.dart';
import '../domain/repositories/internship_approval_detail_repository.dart';
import '../domain/usecases/get_teacher_internship_plans_usecase.dart';
import '../domain/usecases/get_internship_plan_detail_usecase.dart';
import '../domain/usecases/get_internship_plan_list_usecase.dart';
import '../domain/usecases/get_free_internship_exempt_list_usecase.dart';
import '../domain/usecases/approve_free_internship_exempt_usecase.dart';
import '../domain/usecases/submit_internship_application_usecase.dart';
import 'package:flutter_demo/features/internship/domain/usecases/get_internship_approval_list_usecase.dart';
import '../domain/usecases/get_internship_approval_detail_usecase.dart';
import '../domain/usecases/approve_internship_application_usecase.dart';
import '../presentation/bloc/teacher_plan_list/teacher_plan_list_bloc.dart';
import '../presentation/bloc/plan_detail/plan_detail_bloc.dart';
import '../presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import '../presentation/bloc/free_internship_exempt_list/free_internship_exempt_list_bloc.dart';
import '../presentation/bloc/free_internship_approval/free_internship_approval_bloc.dart';
import '../presentation/bloc/internship_application_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_list_bloc.dart';
import '../presentation/bloc/internship_approval_detail/internship_approval_detail_bloc.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化实习模块依赖
///
/// 注册实习模块的数据源、仓库、用例和BLoC
Future<void> setupInternshipDependencies() async {
  // 数据源
  getIt.registerLazySingleton<InternshipPlanRemoteDataSource>(
    () => InternshipPlanRemoteDataSourceImpl(getIt<DioClient>()),
  );

  getIt.registerLazySingleton<FreeInternshipExemptRemoteDataSource>(
    () => FreeInternshipExemptRemoteDataSourceImpl(getIt<DioClient>()),
  );

  getIt.registerLazySingleton<InternshipApplicationRemoteDataSource>(
    () => InternshipApplicationRemoteDataSourceImpl(dioClient: getIt<DioClient>()),
  );

  getIt.registerLazySingleton<InternshipApprovalRemoteDataSource>(
    () => InternshipApprovalRemoteDataSourceImpl(dioClient: getIt<DioClient>()),
  );

  getIt.registerLazySingleton<InternshipApprovalDetailRemoteDataSource>(
    () => InternshipApprovalDetailRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 仓库
  getIt.registerLazySingleton<InternshipPlanRepository>(
    () => InternshipPlanRepositoryImpl(
      getIt<InternshipPlanRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  getIt.registerLazySingleton<FreeInternshipExemptRepository>(
    () => FreeInternshipExemptRepositoryImpl(
      getIt<FreeInternshipExemptRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  getIt.registerLazySingleton<InternshipApplicationRepository>(
    () => InternshipApplicationRepositoryImpl(
      remoteDataSource: getIt<InternshipApplicationRemoteDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );

  getIt.registerLazySingleton<InternshipApprovalRepository>(
    () => InternshipApprovalRepositoryImpl(
      remoteDataSource: getIt<InternshipApprovalRemoteDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );

  getIt.registerLazySingleton<InternshipApprovalDetailRepository>(
    () => InternshipApprovalDetailRepositoryImpl(
      remoteDataSource: getIt<InternshipApprovalDetailRemoteDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );

  // 用例
  getIt.registerFactory<GetTeacherInternshipPlansUseCase>(
    () => GetTeacherInternshipPlansUseCase(getIt<InternshipPlanRepository>()),
  );

  getIt.registerFactory<GetInternshipPlanDetailUseCase>(
    () => GetInternshipPlanDetailUseCase(getIt<InternshipPlanRepository>()),
  );

  getIt.registerFactory<GetInternshipPlanListUseCase>(
    () => GetInternshipPlanListUseCase(getIt<InternshipPlanRepository>()),
  );

  getIt.registerFactory<GetFreeInternshipExemptListUseCase>(
    () => GetFreeInternshipExemptListUseCase(getIt<FreeInternshipExemptRepository>()),
  );

  getIt.registerFactory<ApproveFreeInternshipExemptUseCase>(
    () => ApproveFreeInternshipExemptUseCase(getIt<FreeInternshipExemptRepository>()),
  );

  getIt.registerFactory<SubmitInternshipApplicationUseCase>(
    () => SubmitInternshipApplicationUseCase(
      repository: getIt<InternshipApplicationRepository>(),
    ),
  );

  getIt.registerFactory<GetInternshipApprovalListUseCase>(
    () => GetInternshipApprovalListUseCase(
      repository: getIt<InternshipApprovalRepository>(),
    ),
  );

  getIt.registerFactory<GetInternshipApprovalDetailUseCase>(
    () => GetInternshipApprovalDetailUseCase(
      repository: getIt<InternshipApprovalDetailRepository>(),
    ),
  );

  getIt.registerFactory<ApproveInternshipApplicationUseCase>(
    () => ApproveInternshipApplicationUseCase(
      repository: getIt<InternshipApprovalDetailRepository>(),
    ),
  );

  // BLoC
  getIt.registerFactory<TeacherPlanListBloc>(
    () => TeacherPlanListBloc(
      getTeacherInternshipPlansUseCase: getIt<GetTeacherInternshipPlansUseCase>(),
    ),
  );

  getIt.registerFactory<PlanDetailBloc>(
    () => PlanDetailBloc(getIt<GetInternshipPlanDetailUseCase>()),
  );

  getIt.registerFactory<FreeInternshipExemptListBloc>(
    () => FreeInternshipExemptListBloc(
      getFreeInternshipExemptListUseCase: getIt<GetFreeInternshipExemptListUseCase>(),
    ),
  );

  getIt.registerFactory<FreeInternshipApprovalBloc>(
    () => FreeInternshipApprovalBloc(
      approveFreeInternshipExemptUseCase: getIt<ApproveFreeInternshipExemptUseCase>(),
    ),
  );

  // 全局实习计划列表BLoC - 使用单例模式，确保全局状态一致
  getIt.registerLazySingleton<PlanListGlobalBloc>(
    () => PlanListGlobalBloc(
      getInternshipPlanListUseCase: getIt<GetInternshipPlanListUseCase>(),
    ),
  );

  // 实习申请BLoC - 使用工厂模式，每次使用时创建新实例
  getIt.registerFactory<InternshipApplicationBloc>(
    () => InternshipApplicationBloc(
      submitInternshipApplicationUseCase: getIt<SubmitInternshipApplicationUseCase>(),
      planListGlobalBloc: getIt<PlanListGlobalBloc>(),
      localStorage: getIt<LocalStorage>(),
    ),
  );

  // 实习申请审批列表BLoC - 使用工厂模式，每次使用时创建新实例
  getIt.registerFactory<InternshipApprovalListBloc>(
    () => InternshipApprovalListBloc(
      getInternshipApprovalListUseCase: getIt<GetInternshipApprovalListUseCase>(),
    ),
  );

  // 实习申请审批详情BLoC - 使用工厂模式，每次使用时创建新实例
  getIt.registerFactory<InternshipApprovalDetailBloc>(
    () => InternshipApprovalDetailBloc(
      getInternshipApprovalDetailUseCase: getIt<GetInternshipApprovalDetailUseCase>(),
      approveInternshipApplicationUseCase: getIt<ApproveInternshipApplicationUseCase>(),
    ),
  );
}
