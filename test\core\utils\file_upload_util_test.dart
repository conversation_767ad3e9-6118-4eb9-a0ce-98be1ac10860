/// -----
/// file_upload_util_test.dart
/// 
/// 通用文件上传工具测试
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/core/utils/file_upload_util.dart';

void main() {
  group('FileUploadUtil static methods', () {
    group('isPdfFile', () {
      test('should return true for PDF files', () {
        expect(FileUploadUtil.isPdfFile('document.pdf'), true);
        expect(FileUploadUtil.isPdfFile('Document.PDF'), true);
        expect(FileUploadUtil.isPdfFile('test.pdf'), true);
        expect(FileUploadUtil.isPdfFile('report.Pdf'), true);
      });

      test('should return false for non-PDF files', () {
        expect(FileUploadUtil.isPdfFile('document.doc'), false);
        expect(FileUploadUtil.isPdfFile('image.jpg'), false);
        expect(FileUploadUtil.isPdfFile('text.txt'), false);
        expect(FileUploadUtil.isPdfFile('presentation.ppt'), false);
        expect(FileUploadUtil.isPdfFile('spreadsheet.xlsx'), false);
      });

      test('should return false for files without extension', () {
        expect(FileUploadUtil.isPdfFile('document'), false);
        expect(FileUploadUtil.isPdfFile(''), false);
      });
    });

    group('isValidFileSize', () {
      test('should return true for valid file sizes', () {
        expect(FileUploadUtil.isValidFileSize(0), true); // 0 bytes
        expect(FileUploadUtil.isValidFileSize(1024), true); // 1KB
        expect(FileUploadUtil.isValidFileSize(5 * 1024 * 1024), true); // 5MB
        expect(FileUploadUtil.isValidFileSize(10 * 1024 * 1024), true); // 10MB (exactly at limit)
      });

      test('should return false for oversized files', () {
        expect(FileUploadUtil.isValidFileSize(10 * 1024 * 1024 + 1), false); // 10MB + 1 byte
        expect(FileUploadUtil.isValidFileSize(11 * 1024 * 1024), false); // 11MB
        expect(FileUploadUtil.isValidFileSize(20 * 1024 * 1024), false); // 20MB
        expect(FileUploadUtil.isValidFileSize(100 * 1024 * 1024), false); // 100MB
      });
    });

    group('formatFileSize', () {
      test('should format bytes correctly', () {
        expect(FileUploadUtil.formatFileSize(0), '0B');
        expect(FileUploadUtil.formatFileSize(512), '512B');
        expect(FileUploadUtil.formatFileSize(1023), '1023B');
      });

      test('should format kilobytes correctly', () {
        expect(FileUploadUtil.formatFileSize(1024), '1.0KB');
        expect(FileUploadUtil.formatFileSize(1536), '1.5KB');
        expect(FileUploadUtil.formatFileSize(2048), '2.0KB');
        expect(FileUploadUtil.formatFileSize(1024 * 1024 - 1), '1024.0KB');
      });

      test('should format megabytes correctly', () {
        expect(FileUploadUtil.formatFileSize(1024 * 1024), '1.0MB');
        expect(FileUploadUtil.formatFileSize((1.5 * 1024 * 1024).round()), '1.5MB');
        expect(FileUploadUtil.formatFileSize((2.5 * 1024 * 1024).round()), '2.5MB');
        expect(FileUploadUtil.formatFileSize(10 * 1024 * 1024), '10.0MB');
      });
    });

    group('maxFileSize', () {
      test('should return correct max file size', () {
        expect(FileUploadUtil.maxFileSize, 10 * 1024 * 1024); // 10MB
      });
    });
  });

  group('FileUploadResult', () {
    test('should create FileUploadResult with correct properties', () {
      // arrange
      const result = FileUploadResult(
        fileName: 'test.pdf',
        fileUrl: 'https://example.com/files/test.pdf',
      );

      // assert
      expect(result.fileName, 'test.pdf');
      expect(result.fileUrl, 'https://example.com/files/test.pdf');
    });

    test('should create FileUploadResult from response', () {
      // arrange
      final response = {
        'fileName': 'test.pdf',
        'fileUrl': 'https://example.com/files/test.pdf',
      };

      // act
      final result = FileUploadResult.fromResponse(response);

      // assert
      expect(result.fileName, 'test.pdf');
      expect(result.fileUrl, 'https://example.com/files/test.pdf');
    });

    test('should handle missing values in response', () {
      // arrange
      final response = <String, dynamic>{};

      // act
      final result = FileUploadResult.fromResponse(response);

      // assert
      expect(result.fileName, '');
      expect(result.fileUrl, '');
    });
  });

  group('FileUploadType', () {
    test('should have correct values', () {
      expect(FileUploadType.general.value, '10');
      expect(FileUploadType.pdf.value, '13');
    });
  });
}
