/// -----
/// pdf_upload_util.dart
///
/// PDF文件上传工具类（已废弃，请使用FileUploadUtil）
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'file_upload_util.dart';

/// PDF文件上传结果（已废弃，请使用FileUploadResult）
@Deprecated('请使用FileUploadResult')
class PdfUploadResult {
  /// 文件名
  final String fileName;

  /// 文件URL
  final String fileUrl;

  const PdfUploadResult({
    required this.fileName,
    required this.fileUrl,
  });
}

/// PDF文件上传工具类（已废弃，请使用FileUploadUtil）
@Deprecated('请使用FileUploadUtil')
class PdfUploadUtil {
  final FileUploadUtil _fileUploadUtil;

  PdfUploadUtil(this._fileUploadUtil);

  /// 上传PDF文件到服务器（已废弃，请使用FileUploadUtil.uploadPdfFile）
  @Deprecated('请使用FileUploadUtil.uploadPdfFile')
  Future<PdfUploadResult> uploadPdfFile({
    required String filePath,
    required String fileName,
    Function(double progress)? onProgress,
  }) async {
    final result = await _fileUploadUtil.uploadPdfFile(
      filePath: filePath,
      fileName: fileName,
      onProgress: onProgress,
    );

    return PdfUploadResult(
      fileName: result.fileName,
      fileUrl: result.fileUrl,
    );
  }

  /// 验证文件是否为PDF格式（已废弃，请使用FileUploadUtil.isPdfFile）
  @Deprecated('请使用FileUploadUtil.isPdfFile')
  static bool isPdfFile(String fileName) {
    return FileUploadUtil.isPdfFile(fileName);
  }

  /// 获取文件大小限制（已废弃，请使用FileUploadUtil.maxFileSize）
  @Deprecated('请使用FileUploadUtil.maxFileSize')
  static int get maxFileSize => FileUploadUtil.maxFileSize;

  /// 验证文件大小（已废弃，请使用FileUploadUtil.isValidFileSize）
  @Deprecated('请使用FileUploadUtil.isValidFileSize')
  static bool isValidFileSize(int fileSize) {
    return FileUploadUtil.isValidFileSize(fileSize);
  }

  /// 格式化文件大小显示（已废弃，请使用FileUploadUtil.formatFileSize）
  @Deprecated('请使用FileUploadUtil.formatFileSize')
  static String formatFileSize(int bytes) {
    return FileUploadUtil.formatFileSize(bytes);
  }
}
