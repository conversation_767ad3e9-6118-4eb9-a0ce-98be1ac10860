/// -----
/// internship_approval_detail_bloc.dart
///
/// 实习审批详情BLoC
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import '../../../domain/entities/internship_approval_request.dart';
import '../../../domain/usecases/get_internship_approval_detail_usecase.dart';
import '../../../domain/usecases/approve_internship_application_usecase.dart';
import 'internship_approval_detail_event.dart';
import 'internship_approval_detail_state.dart';

/// 实习审批详情BLoC
///
/// 处理实习审批详情相关的业务逻辑
class InternshipApprovalDetailBloc extends Bloc<InternshipApprovalDetailEvent, InternshipApprovalDetailState> {
  /// 获取实习审批详情用例
  final GetInternshipApprovalDetailUseCase getInternshipApprovalDetailUseCase;

  /// 审批实习申请用例
  final ApproveInternshipApplicationUseCase approveInternshipApplicationUseCase;

  static const String _tag = 'InternshipApprovalDetailBloc';

  /// 构造函数
  InternshipApprovalDetailBloc({
    required this.getInternshipApprovalDetailUseCase,
    required this.approveInternshipApplicationUseCase,
  }) : super(InternshipApprovalDetailInitial()) {
    on<LoadInternshipApprovalDetailEvent>(_onLoadInternshipApprovalDetail);
    on<ApproveInternshipApplicationEvent>(_onApproveInternshipApplication);
  }

  /// 处理加载实习审批详情事件
  Future<void> _onLoadInternshipApprovalDetail(
    LoadInternshipApprovalDetailEvent event,
    Emitter<InternshipApprovalDetailState> emit,
  ) async {
    try {
      Logger.info(_tag, '开始加载实习审批详情，ID: ${event.id}');
      
      emit(InternshipApprovalDetailLoading());

      // 调用用例获取详情
      final result = await getInternshipApprovalDetailUseCase(
        GetInternshipApprovalDetailParams(id: event.id),
      );

      result.fold(
        (failure) {
          Logger.error(_tag, '加载实习审批详情失败: ${failure.message}');
          emit(InternshipApprovalDetailError(message: failure.message));
        },
        (detail) {
          Logger.info(_tag, '成功加载实习审批详情');
          emit(InternshipApprovalDetailLoaded(detail: detail));
        },
      );
    } catch (e) {
      Logger.error(_tag, '加载实习审批详情异常: $e');
      emit(InternshipApprovalDetailError(message: '加载详情失败: $e'));
    }
  }

  /// 处理审批实习申请事件
  Future<void> _onApproveInternshipApplication(
    ApproveInternshipApplicationEvent event,
    Emitter<InternshipApprovalDetailState> emit,
  ) async {
    try {
      Logger.info(_tag, '开始审批实习申请 - ID: ${event.id}, 通过: ${event.isApproved}');
      
      // 获取当前状态的详情数据
      final currentState = state;
      if (currentState is! InternshipApprovalDetailLoaded) {
        Logger.error(_tag, '当前状态不是已加载状态，无法执行审批操作');
        return;
      }

      emit(InternshipApprovalProcessing(detail: currentState.detail));

      // 创建审批请求
      final request = event.isApproved
          ? InternshipApprovalRequest.approve(
              id: event.id,
              reviewOpinion: event.reviewOpinion,
            )
          : InternshipApprovalRequest.reject(
              id: event.id,
              reviewOpinion: event.reviewOpinion,
            );

      // 执行审批
      final result = await approveInternshipApplicationUseCase(
        ApproveInternshipApplicationParams(request: request),
      );

      result.fold(
        (failure) {
          Logger.error(_tag, '审批实习申请失败: ${failure.message}');
          emit(InternshipApprovalFailure(
            detail: currentState.detail,
            message: failure.message,
          ));
        },
        (_) {
          Logger.info(_tag, '审批实习申请成功');
          
          // 更新详情数据的状态
          final updatedDetail = currentState.detail.copyWith(
            status: event.isApproved ? '已通过' : '已驳回',
          );

          final successMessage = event.isApproved ? '审批通过' : '审批驳回';
          emit(InternshipApprovalSuccess(
            detail: updatedDetail,
            message: successMessage,
          ));
        },
      );
    } catch (e) {
      Logger.error(_tag, '审批实习申请异常: $e');
      
      // 获取当前详情数据
      final currentState = state;
      final detail = currentState is InternshipApprovalDetailLoaded 
          ? currentState.detail 
          : currentState is InternshipApprovalProcessing
              ? currentState.detail
              : null;
      
      if (detail != null) {
        emit(InternshipApprovalFailure(
          detail: detail,
          message: '审批操作失败: $e',
        ));
      } else {
        emit(InternshipApprovalDetailError(message: '审批操作失败: $e'));
      }
    }
  }
}
