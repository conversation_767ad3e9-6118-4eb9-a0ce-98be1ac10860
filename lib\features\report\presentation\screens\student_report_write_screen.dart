/// -----
/// student_report_write_screen.dart
/// 
/// 写报告页面，支持日报、周报、月报、总结的编写
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/loading_widget.dart';
import 'package:flutter_demo/core/widgets/empty_state_widget.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_bloc.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_state.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/features/report/presentation/widgets/report_content_widget.dart';
import 'package:flutter_demo/features/report/presentation/widgets/attachment_uploader.dart';
import 'package:flutter_demo/core/router/route_constants.dart';
import 'package:go_router/go_router.dart';

class ReportWriteScreen extends StatelessWidget {
  final ReportType reportType;
  final String? reportId;

  const ReportWriteScreen({
    Key? key,
    required this.reportType,
    this.reportId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ReportWriteBloc>()
        ..add(InitializeReportWriteEvent(
          reportType: reportType,
          reportId: reportId,
        )),
      child: ReportWriteView(reportType: reportType),
    );
  }
}

class ReportWriteView extends StatefulWidget {
  final ReportType reportType;

  const ReportWriteView({
    Key? key,
    required this.reportType,
  }) : super(key: key);

  @override
  State<ReportWriteView> createState() => _ReportWriteViewState();
}

class _ReportWriteViewState extends State<ReportWriteView> {
  final TextEditingController _contentController = TextEditingController();

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _getTitle(),
        actions: [
          TextButton(
            onPressed: () => _showHistoryReports(context),
            child: Text(
              _getHistoryButtonText(),
              style: TextStyle(
                fontSize: 28.sp,
                color: const Color(0xFF2165F6),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      body: BlocConsumer<ReportWriteBloc, ReportWriteState>(
        listener: (context, state) {
          if (state is ReportWriteSubmitSuccess) {
            AppSnackBar.showSuccess(context, state.message);
            context.pop(true); // 返回并标记提交成功
          } else if (state is ReportWriteSaveDraftSuccess) {
            AppSnackBar.showSuccess(context, state.message);
          } else if (state is ReportWriteAttachmentUploadSuccess) {
            AppSnackBar.showSuccess(context, state.message);
          } else if (state is ReportWriteAttachmentUploadError) {
            AppSnackBar.showError(context, state.message);
          } else if (state is ReportWriteError) {
            AppSnackBar.showError(context, state.message);
          } else if (state is ReportWriteLoaded) {
            // 更新文本控制器内容
            if (_contentController.text != state.content) {
              _contentController.text = state.content;
            }
          }
        },
        builder: (context, state) {
          if (state is ReportWriteLoading) {
            return const LoadingWidget();
          }

          if (state is ReportWriteError && state is! ReportWriteLoaded) {
            return EmptyStateWidget(
              icon: Icons.error_outline,
              title: '加载失败',
              message: state.message,
              buttonText: '重试',
              onButtonPressed: () => context.read<ReportWriteBloc>().add(
                InitializeReportWriteEvent(reportType: widget.reportType),
              ),
            );
          }

          if (state is! ReportWriteLoaded) {
            return const EmptyStateWidget(
              icon: Icons.inbox,
              title: '页面加载中',
              message: '请稍候...',
            );
          }

          return _buildContent(context, state);
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, ReportWriteLoaded state) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 课程选择器
                const CourseHeaderSection(),
                
                SizedBox(height: 20.h),
                
                // 基础信息
                _buildBasicInfo(state),
                
                SizedBox(height: 40.h),
                
                // 报告内容
                ReportContentWidget(
                  reportType: widget.reportType,
                  controller: _contentController,
                  hasAIContent: state.hasAIContent,
                  isGeneratingAI: state is ReportWriteGeneratingAI,
                  reportTipQuestions: state.reportTipQuestions,
                  onContentChanged: (content) {
                    context.read<ReportWriteBloc>().add(
                      UpdateReportContentEvent(content),
                    );
                  },
                  onGenerateAI: () {
                    context.read<ReportWriteBloc>().add(
                      GenerateAIContentEvent(widget.reportType),
                    );
                  },
                ),
                
                SizedBox(height: 40.h),
                
                // 附件上传
                _buildAttachmentSection(state),
                
                SizedBox(height: 35.h), // 为底部按钮留出空间
              ],
            ),
          ),
        ),
        
        // 底部按钮
        _buildBottomButtons(context, state),
      ],
    );
  }

  Widget _buildBasicInfo(ReportWriteLoaded state) {
    return Container(
      color: Colors.white,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 25.w,vertical: 40.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '基础信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 24.h),
          Text(
            _getBasicInfoText(),
            style: TextStyle(
              fontSize: 28.sp,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentSection(ReportWriteLoaded state) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 25.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25.w),
            child: Text(
              '附件上传',
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF333333),
              ),
            ),
          ),
          SizedBox(height: 24.h),
          AttachmentUploader(
            attachments: state.attachments,
            onFileSelected: (filePath, fileName, fileSize) {
              context.read<ReportWriteBloc>().add(AddAttachmentEvent(
                filePath: filePath,
                fileName: fileName,
                fileSize: fileSize,
              ));
            },
            onRemoveFile: (index) {
              context.read<ReportWriteBloc>().add(RemoveAttachmentEvent(index));
            },
            isUploading: state is ReportWriteUploadingAttachment,
            uploadProgress: state is ReportWriteUploadingAttachment
                ? state.uploadProgress
                : 0.0,
            uploadingFileName: state is ReportWriteUploadingAttachment
                ? state.uploadingFileName
                : null,
            maxFiles: 1, // 限制1个文件
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons(BuildContext context, ReportWriteLoaded state) {
    return Container(
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: state.isSubmitting ? null : () {
              context.read<ReportWriteBloc>().add(const SubmitReportEvent());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2165F6),
              padding: EdgeInsets.symmetric(vertical: 28.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              elevation: 0,
            ),
            child: state.isSubmitting
                ? SizedBox(
                    width: 40.w,
                    height: 40.w,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    '提交',
                    style: TextStyle(
                      fontSize: 32.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  String _getTitle() {
    switch (widget.reportType) {
      case ReportType.daily:
        return '写日报';
      case ReportType.weekly:
        return '写周报';
      case ReportType.monthly:
        return '写月报';
      case ReportType.summary:
        return '写总结';
    }
  }

  String _getHistoryButtonText() {
    switch (widget.reportType) {
      case ReportType.daily:
        return '历史日报';
      case ReportType.weekly:
        return '历史周报';
      case ReportType.monthly:
        return '历史月报';
      case ReportType.summary:
        return '历史总结';
    }
  }

  String _getBasicInfoText() {
    final now = DateTime.now();
    switch (widget.reportType) {
      case ReportType.daily:
        return '${now.year}.${now.month.toString().padLeft(2, '0')}.${now.day.toString().padLeft(2, '0')}';
      case ReportType.weekly:
        // 计算本周的开始和结束日期
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        final endOfWeek = startOfWeek.add(const Duration(days: 6));
        return '第${_getWeekOfYear(now)}周 ${startOfWeek.year}.${startOfWeek.month.toString().padLeft(2, '0')}.${startOfWeek.day.toString().padLeft(2, '0')}-${endOfWeek.year}.${endOfWeek.month.toString().padLeft(2, '0')}.${endOfWeek.day.toString().padLeft(2, '0')}';
      case ReportType.monthly:
        return '${now.year}年${now.month}月';
      case ReportType.summary:
        return '实习总结';
    }
  }

  int _getWeekOfYear(DateTime date) {
    final startOfYear = DateTime(date.year);
    final daysSinceStartOfYear = date.difference(startOfYear).inDays;
    return (daysSinceStartOfYear / 7).ceil();
  }

  void _showHistoryReports(BuildContext context) {
    // 根据报告类型跳转到对应的学生报告列表页面
    String routePath;
    switch (widget.reportType) {
      case ReportType.daily:
        routePath = AppRoutes.studentDailyReport;
        break;
      case ReportType.weekly:
        routePath = AppRoutes.studentWeeklyReport;
        break;
      case ReportType.monthly:
        routePath = AppRoutes.studentMonthlyReport;
        break;
      case ReportType.summary:
        routePath = AppRoutes.studentSummaryReport;
        break;
    }

    // 跳转到对应的历史报告列表页面
    context.push(routePath);
  }
}
