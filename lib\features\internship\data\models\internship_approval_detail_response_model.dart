/// -----
/// internship_approval_detail_response_model.dart
///
/// 实习审批详情API响应模型
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 实习审批详情API响应模型
///
/// 对应API接口 /v1/internship/apply/teacher/detail 的响应数据结构
class InternshipApprovalDetailResponseModel {
  /// 详细地址
  final String address;
  
  /// 市
  final String city;
  
  /// 统一社会信用代码
  final String companyCode;
  
  /// 企业名称
  final String companyName;
  
  /// 企业老师
  final String companyTeacher;
  
  /// 企业邮箱
  final String contactEmail;
  
  /// 企业联系人
  final String contactPerson;
  
  /// 企业联系人电话
  final String contactPhone;
  
  /// 工作内容
  final String description;
  
  /// 文件列表
  final List<FileInfoModel> fileList;
  
  /// 实习申请ID
  final int id;
  
  /// 所属行业
  final String industry;
  
  /// 岗位详细地址
  final String jobAddress;
  
  /// 岗位类别
  final String jobCategory;
  
  /// 岗位市
  final String jobCity;
  
  /// 岗位部门
  final String jobDept;
  
  /// 岗位结束时间（时间戳毫秒）
  final int jobEndTime;
  
  /// 是否提供伙食（0=否，1=是）
  final int jobFood;
  
  /// 住宿类型
  final String jobHouse;
  
  /// 住宿地址
  final String jobHouseAddress;
  
  /// 专业匹配
  final String jobMajor;
  
  /// 岗位名称
  final String jobName;
  
  /// 岗位省
  final String jobProvince;
  
  /// 实习薪资
  final String jobSalary;
  
  /// 实习工作是否有特殊情况（0=无，1=有）
  final int jobSpecial;
  
  /// 岗位开始时间（时间戳毫秒）
  final int jobStartTime;
  
  /// 实习方式
  final String jobType;
  
  /// 企业性质
  final String nature;
  
  /// 关联的实习计划ID
  final int planId;
  
  /// 省份
  final String province;
  
  /// 企业规模
  final String scale;
  
  /// 企业老师电话
  final String teacherPhone;

  const InternshipApprovalDetailResponseModel({
    required this.address,
    required this.city,
    required this.companyCode,
    required this.companyName,
    required this.companyTeacher,
    required this.contactEmail,
    required this.contactPerson,
    required this.contactPhone,
    required this.description,
    required this.fileList,
    required this.id,
    required this.industry,
    required this.jobAddress,
    required this.jobCategory,
    required this.jobCity,
    required this.jobDept,
    required this.jobEndTime,
    required this.jobFood,
    required this.jobHouse,
    required this.jobHouseAddress,
    required this.jobMajor,
    required this.jobName,
    required this.jobProvince,
    required this.jobSalary,
    required this.jobSpecial,
    required this.jobStartTime,
    required this.jobType,
    required this.nature,
    required this.planId,
    required this.province,
    required this.scale,
    required this.teacherPhone,
  });

  /// 从JSON创建模型
  factory InternshipApprovalDetailResponseModel.fromJson(Map<String, dynamic> json) {
    return InternshipApprovalDetailResponseModel(
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      companyCode: json['companyCode'] ?? '',
      companyName: json['companyName'] ?? '',
      companyTeacher: json['companyTeacher'] ?? '',
      contactEmail: json['contactEmail'] ?? '',
      contactPerson: json['contactPerson'] ?? '',
      contactPhone: json['contactPhone'] ?? '',
      description: json['description'] ?? '',
      fileList: (json['fileList'] as List<dynamic>?)
          ?.map((item) => FileInfoModel.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      id: _parseIntFromJson(json['id']),
      industry: json['industry'] ?? '',
      jobAddress: json['jobAddress'] ?? '',
      jobCategory: json['jobCategory'] ?? '',
      jobCity: json['jobCity'] ?? '',
      jobDept: json['jobDept'] ?? '',
      jobEndTime: _parseIntFromJson(json['jobEndTime']),
      jobFood: _parseIntFromJson(json['jobFood']),
      jobHouse: json['jobHouse'] ?? '',
      jobHouseAddress: json['jobHouseAddress'] ?? '',
      jobMajor: json['jobMajor'] ?? '',
      jobName: json['jobName'] ?? '',
      jobProvince: json['jobProvince'] ?? '',
      jobSalary: json['jobSalary'] ?? '',
      jobSpecial: _parseIntFromJson(json['jobSpecial']),
      jobStartTime: _parseIntFromJson(json['jobStartTime']),
      jobType: json['jobType'] ?? '',
      nature: json['nature'] ?? '',
      planId: _parseIntFromJson(json['planId']),
      province: json['province'] ?? '',
      scale: json['scale'] ?? '',
      teacherPhone: json['teacherPhone'] ?? '',
    );
  }

  /// 安全地从JSON值解析整数
  ///
  /// 处理API返回的字符串类型数字字段，将其转换为int类型
  static int _parseIntFromJson(dynamic value) {
    if (value == null) {
      return 0;
    }
    if (value is int) {
      return value;
    }
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'city': city,
      'companyCode': companyCode,
      'companyName': companyName,
      'companyTeacher': companyTeacher,
      'contactEmail': contactEmail,
      'contactPerson': contactPerson,
      'contactPhone': contactPhone,
      'description': description,
      'fileList': fileList.map((item) => item.toJson()).toList(),
      'id': id,
      'industry': industry,
      'jobAddress': jobAddress,
      'jobCategory': jobCategory,
      'jobCity': jobCity,
      'jobDept': jobDept,
      'jobEndTime': jobEndTime,
      'jobFood': jobFood,
      'jobHouse': jobHouse,
      'jobHouseAddress': jobHouseAddress,
      'jobMajor': jobMajor,
      'jobName': jobName,
      'jobProvince': jobProvince,
      'jobSalary': jobSalary,
      'jobSpecial': jobSpecial,
      'jobStartTime': jobStartTime,
      'jobType': jobType,
      'nature': nature,
      'planId': planId,
      'province': province,
      'scale': scale,
      'teacherPhone': teacherPhone,
    };
  }
}

/// 文件信息模型
class FileInfoModel {
  /// 文件代码
  final int fileCode;
  
  /// 文件名称
  final String fileName;
  
  /// 文件类型
  final String fileType;
  
  /// 文件URL
  final String fileUrl;
  
  /// 是否必需
  final int isRequired;

  const FileInfoModel({
    required this.fileCode,
    required this.fileName,
    required this.fileType,
    required this.fileUrl,
    required this.isRequired,
  });

  /// 从JSON创建模型
  factory FileInfoModel.fromJson(Map<String, dynamic> json) {
    return FileInfoModel(
      fileCode: InternshipApprovalDetailResponseModel._parseIntFromJson(json['fileCode']),
      fileName: json['fileName'] ?? '',
      fileType: json['fileType'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
      isRequired: InternshipApprovalDetailResponseModel._parseIntFromJson(json['isRequired']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'fileCode': fileCode,
      'fileName': fileName,
      'fileType': fileType,
      'fileUrl': fileUrl,
      'isRequired': isRequired,
    };
  }
}
