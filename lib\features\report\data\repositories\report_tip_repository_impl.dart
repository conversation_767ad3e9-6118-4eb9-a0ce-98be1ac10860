/// -----
/// report_tip_repository_impl.dart
/// 
/// 报告提示词仓库实现
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/error/failures/server_failure.dart' as sf;
import '../../domain/entities/report_tip.dart';
import '../../domain/repositories/report_tip_repository.dart';
import '../datasources/remote/report_tip_remote_data_source.dart';

/// 报告提示词仓库实现
class ReportTipRepositoryImpl implements ReportTipRepository {
  final ReportTipRemoteDataSource _remoteDataSource;

  ReportTipRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, ReportTip>> getReportTip({
    required String planId,
    required int type,
  }) async {
    try {
      final reportTipModel = await _remoteDataSource.getReportTip(
        planId: planId,
        type: type,
      );
      
      return Right(reportTipModel.toEntity());
    } on ServerException catch (e) {
      return Left(sf.ServerFailure(e.message));
    } catch (e) {
      return Left(sf.ServerFailure('获取报告提示词失败: $e'));
    }
  }
}
