/// -----
/// report_tip_repository.dart
/// 
/// 报告提示词仓库接口
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/report_tip.dart';

/// 报告提示词仓库接口
abstract class ReportTipRepository {
  /// 获取报告提示词
  ///
  /// [planId] 实习计划ID
  /// [type] 类型（1=日报, 2=周报）
  /// 返回 Either<Failure, ReportTip>，表示获取成功或失败
  Future<Either<Failure, ReportTip>> getReportTip({
    required String planId,
    required int type,
  });
}
