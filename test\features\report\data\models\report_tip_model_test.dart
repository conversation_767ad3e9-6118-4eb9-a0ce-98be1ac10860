/// -----
/// report_tip_model_test.dart
/// 
/// 报告提示词数据模型测试
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/report/data/models/report_tip_model.dart';

void main() {
  group('ReportTipModel', () {
    test('should create model from JSON with int values', () {
      // arrange
      final json = {
        'planId': 123,
        'tip': '1.今天的主要工作是什么？\n2.您在工作中有哪些思考？',
        'type': 1,
      };

      // act
      final model = ReportTipModel.fromJson(json);

      // assert
      expect(model.planId, 123);
      expect(model.tip, '1.今天的主要工作是什么？\n2.您在工作中有哪些思考？');
      expect(model.type, 1);
    });

    test('should create model from JSON with string values', () {
      // arrange
      final json = {
        'planId': '123',
        'tip': '1.今天的主要工作是什么？\n2.您在工作中有哪些思考？',
        'type': '1',
      };

      // act
      final model = ReportTipModel.fromJson(json);

      // assert
      expect(model.planId, 123);
      expect(model.tip, '1.今天的主要工作是什么？\n2.您在工作中有哪些思考？');
      expect(model.type, 1);
    });

    test('should handle invalid string values with defaults', () {
      // arrange
      final json = {
        'planId': 'invalid',
        'tip': '测试提示',
        'type': 'invalid',
      };

      // act
      final model = ReportTipModel.fromJson(json);

      // assert
      expect(model.planId, 0); // default value
      expect(model.tip, '测试提示');
      expect(model.type, 1); // default value
    });

    test('should handle missing values with defaults', () {
      // arrange
      final json = <String, dynamic>{};

      // act
      final model = ReportTipModel.fromJson(json);

      // assert
      expect(model.planId, 0);
      expect(model.tip, '');
      expect(model.type, 1);
    });

    test('should handle null values with defaults', () {
      // arrange
      final json = {
        'planId': null,
        'tip': null,
        'type': null,
      };

      // act
      final model = ReportTipModel.fromJson(json);

      // assert
      expect(model.planId, 0);
      expect(model.tip, '');
      expect(model.type, 1);
    });

    test('should convert to JSON correctly', () {
      // arrange
      const model = ReportTipModel(
        planId: 123,
        tip: '测试提示',
        type: 2,
      );

      // act
      final json = model.toJson();

      // assert
      expect(json, {
        'planId': 123,
        'tip': '测试提示',
        'type': 2,
      });
    });

    test('should convert to entity correctly', () {
      // arrange
      const model = ReportTipModel(
        planId: 123,
        tip: '测试提示',
        type: 2,
      );

      // act
      final entity = model.toEntity();

      // assert
      expect(entity.planId, 123);
      expect(entity.tip, '测试提示');
      expect(entity.type, 2);
    });

    test('should handle real API response format', () {
      // arrange - 模拟真实API响应
      final json = {
        'resultCode': '0',
        'resultMsg': 'success',
        'data': {
          'tip': '',
          'planId': '2',
          'type': '1',
        }
      };

      // act - 只使用data部分
      final model = ReportTipModel.fromJson(json['data'] as Map<String, dynamic>);

      // assert
      expect(model.planId, 2);
      expect(model.tip, '');
      expect(model.type, 1);
    });
  });
}
