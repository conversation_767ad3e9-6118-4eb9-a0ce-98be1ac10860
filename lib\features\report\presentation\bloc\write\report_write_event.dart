/// -----
/// report_write_event.dart
/// 
/// 写报告页面的事件定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';

abstract class ReportWriteEvent extends Equatable {
  const ReportWriteEvent();

  @override
  List<Object?> get props => [];
}

/// 初始化写报告页面
class InitializeReportWriteEvent extends ReportWriteEvent {
  final ReportType reportType;
  final String? reportId; // 如果是编辑已有报告，传入报告ID

  const InitializeReportWriteEvent({
    required this.reportType,
    this.reportId,
  });

  @override
  List<Object?> get props => [reportType, reportId];
}

/// 选择课程
class SelectCourseEvent extends ReportWriteEvent {
  final String courseId;
  final String courseName;

  const SelectCourseEvent({
    required this.courseId,
    required this.courseName,
  });

  @override
  List<Object?> get props => [courseId, courseName];
}

/// 更新报告内容
class UpdateReportContentEvent extends ReportWriteEvent {
  final String content;

  const UpdateReportContentEvent(this.content);

  @override
  List<Object?> get props => [content];
}

/// 生成AI内容（仅适用于周报、月报、总结）
class GenerateAIContentEvent extends ReportWriteEvent {
  final ReportType reportType;

  const GenerateAIContentEvent(this.reportType);

  @override
  List<Object?> get props => [reportType];
}

/// 获取报告提示词
class GetReportTipEvent extends ReportWriteEvent {
  final String planId;
  final ReportType reportType;

  const GetReportTipEvent({
    required this.planId,
    required this.reportType,
  });

  @override
  List<Object?> get props => [planId, reportType];
}

/// 选择文件
class SelectFileEvent extends ReportWriteEvent {
  const SelectFileEvent();
}

/// 添加附件
class AddAttachmentEvent extends ReportWriteEvent {
  final String filePath;
  final String fileName;
  final String? fileSize;

  const AddAttachmentEvent({
    required this.filePath,
    required this.fileName,
    this.fileSize,
  });

  @override
  List<Object?> get props => [filePath, fileName, fileSize];
}

/// 删除附件
class RemoveAttachmentEvent extends ReportWriteEvent {
  final int index;

  const RemoveAttachmentEvent(this.index);

  @override
  List<Object?> get props => [index];
}

/// 上传附件到服务器
class UploadAttachmentEvent extends ReportWriteEvent {
  final String filePath;
  final String fileName;

  const UploadAttachmentEvent({
    required this.filePath,
    required this.fileName,
  });

  @override
  List<Object?> get props => [filePath, fileName];
}

/// 保存草稿
class SaveDraftEvent extends ReportWriteEvent {
  const SaveDraftEvent();
}

/// 提交报告
class SubmitReportEvent extends ReportWriteEvent {
  const SubmitReportEvent();
}

/// 获取历史报告列表
class LoadHistoryReportsEvent extends ReportWriteEvent {
  final ReportType reportType;

  const LoadHistoryReportsEvent(this.reportType);

  @override
  List<Object?> get props => [reportType];
}
