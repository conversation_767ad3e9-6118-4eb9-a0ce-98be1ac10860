/// -----
/// report_tip_model.dart
/// 
/// 报告提示词数据模型
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/report_tip.dart';

/// 报告提示词数据模型
class ReportTipModel extends ReportTip {
  const ReportTipModel({
    required super.planId,
    required super.tip,
    required super.type,
  });

  /// 从JSON创建模型
  factory ReportTipModel.fromJson(Map<String, dynamic> json) {
    // 处理planId可能是字符串的情况
    int planIdValue = 0;
    if (json['planId'] is String) {
      planIdValue = int.tryParse(json['planId']) ?? 0;
    } else if (json['planId'] is int) {
      planIdValue = json['planId'];
    }

    // 处理type可能是字符串的情况
    int typeValue = 1;
    if (json['type'] is String) {
      typeValue = int.tryParse(json['type']) ?? 1;
    } else if (json['type'] is int) {
      typeValue = json['type'];
    }

    return ReportTipModel(
      planId: planIdValue,
      tip: json['tip'] as String? ?? '',
      type: typeValue,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'planId': planId,
      'tip': tip,
      'type': type,
    };
  }

  /// 转换为实体
  ReportTip toEntity() {
    return ReportTip(
      planId: planId,
      tip: tip,
      type: type,
    );
  }
}
