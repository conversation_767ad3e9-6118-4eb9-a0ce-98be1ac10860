# 文件上传功能重构文档

## 概述

为了消除代码重复并提高可维护性，我们将原本分散的文件上传功能统一重构为一个通用的文件上传工具类。

## 重构前的问题

1. **代码重复**: `PdfUploadUtil` 和 `FileUploadRemoteDataSource.uploadFileToServer` 存在相似的文件上传逻辑
2. **维护困难**: 两个地方的上传逻辑需要分别维护
3. **类型限制**: PDF上传和一般文件上传使用不同的实现

## 重构后的架构

### 核心组件

#### 1. FileUploadUtil (通用文件上传工具类)
- **位置**: `lib/core/utils/file_upload_util.dart`
- **功能**: 提供统一的文件上传接口
- **支持类型**: 
  - `FileUploadType.general` (type=10) - 一般文件上传
  - `FileUploadType.pdf` (type=13) - PDF文件上传

#### 2. FileUploadResult (上传结果类)
- **属性**: 
  - `fileName`: 文件名
  - `fileUrl`: 文件URL
- **方法**: 
  - `fromResponse()`: 从API响应创建结果对象

#### 3. FileUploadType (上传类型枚举)
- `general('10')`: 一般文件上传
- `pdf('13')`: PDF文件上传

### 主要方法

```dart
// 通用上传方法
Future<FileUploadResult> uploadFile({
  required String filePath,
  required String fileName,
  required FileUploadType uploadType,
  Function(double progress)? onProgress,
});

// PDF文件上传便捷方法
Future<FileUploadResult> uploadPdfFile({
  required String filePath,
  required String fileName,
  Function(double progress)? onProgress,
});

// 一般文件上传便捷方法
Future<FileUploadResult> uploadGeneralFile({
  required String filePath,
  required String fileName,
  Function(double progress)? onProgress,
});
```

### 静态工具方法

```dart
static bool isPdfFile(String fileName)
static int get maxFileSize
static bool isValidFileSize(int fileSize)
static String formatFileSize(int bytes)
```

## 迁移指南

### 1. 报告模块 (ReportWriteBloc)

**之前**:
```dart
final PdfUploadUtil _pdfUploadUtil;
final uploadResult = await _pdfUploadUtil.uploadPdfFile(...);
```

**现在**:
```dart
final FileUploadUtil _fileUploadUtil;
final uploadResult = await _fileUploadUtil.uploadPdfFile(...);
```

### 2. 上传模块 (FileUploadRemoteDataSource)

**之前**:
```dart
// 直接使用DioClient进行文件上传
final formData = FormData.fromMap({
  'type': '10',
  'file': await MultipartFile.fromFile(filePath, filename: fileName),
});
final response = await _dioClient.post('userservice/v1/common/file/upload', data: formData);
```

**现在**:
```dart
// 委托给FileUploadUtil
final result = await _fileUploadUtil.uploadGeneralFile(
  filePath: filePath,
  fileName: fileName,
  onProgress: onProgress,
);
```

### 3. 依赖注入更新

**核心依赖注入** (`lib/core/config/injection/injection.dart`):
```dart
// 注册FileUploadUtil
getIt.registerLazySingleton<FileUploadUtil>(
  () => FileUploadUtil(getIt<DioClient>()),
);
```

**报告模块依赖注入** (`lib/features/report/di/report_injection.dart`):
```dart
// 使用FileUploadUtil替代PdfUploadUtil
getIt.registerFactory<ReportWriteBloc>(
  () => ReportWriteBloc(
    getIt<GetReportTip>(),
    getIt<FileUploadUtil>(),
  ),
);
```

**上传模块依赖注入** (`lib/features/upload/di/upload_injection.dart`):
```dart
// FileUploadRemoteDataSource现在依赖FileUploadUtil
getIt.registerLazySingleton<FileUploadRemoteDataSource>(
  () => FileUploadRemoteDataSourceImpl(getIt<DioClient>(), getIt<FileUploadUtil>()),
);
```

## 向后兼容性

### PdfUploadUtil (已废弃)
- 保留了原有的API接口
- 所有方法都标记为 `@Deprecated`
- 内部委托给 `FileUploadUtil` 实现
- 建议逐步迁移到 `FileUploadUtil`

### 废弃的方法
```dart
@Deprecated('请使用FileUploadUtil.uploadPdfFile')
Future<PdfUploadResult> uploadPdfFile(...)

@Deprecated('请使用FileUploadUtil.isPdfFile')
static bool isPdfFile(String fileName)

@Deprecated('请使用FileUploadUtil.maxFileSize')
static int get maxFileSize

@Deprecated('请使用FileUploadUtil.isValidFileSize')
static bool isValidFileSize(int fileSize)

@Deprecated('请使用FileUploadUtil.formatFileSize')
static String formatFileSize(int bytes)
```

## 测试

### 新增测试文件
- `test/core/utils/file_upload_util_test.dart`: FileUploadUtil的完整单元测试
- 移除了 `test/core/utils/pdf_upload_util_test.dart`

### 测试覆盖
- 文件格式验证
- 文件大小验证
- 文件大小格式化
- 上传结果创建
- 上传类型枚举

## 优势

1. **代码复用**: 统一的文件上传逻辑，减少重复代码
2. **易于维护**: 只需要在一个地方维护上传逻辑
3. **类型安全**: 使用枚举定义上传类型，避免硬编码
4. **向后兼容**: 保留了原有API，支持渐进式迁移
5. **测试完整**: 提供了完整的单元测试覆盖

## 注意事项

1. 新项目应该直接使用 `FileUploadUtil`
2. 现有代码可以继续使用 `PdfUploadUtil`，但会收到废弃警告
3. 建议在合适的时机将现有代码迁移到 `FileUploadUtil`
4. 所有文件上传都通过统一的API端点: `userservice/v1/common/file/upload`
