/// -----
/// student_report_list_screen.dart
///
/// 学生报告列表视图组件，用于显示日报/周报列表，支持展开/收起功能
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';
import 'package:flutter_demo/features/report/data/models/weekly_report.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class StudentReportListView extends StatefulWidget {
  final ReportType type;

  const StudentReportListView({
    Key? key,
    required this.type,
  }) : super(key: key);

  @override
  State<StudentReportListView> createState() => _StudentReportListViewState();
}

class _StudentReportListViewState extends State<StudentReportListView> {
  final String courseName = '2021级市场营销2023-2024学年学期第三学期 财经实习';
  final List<String> availableCourses = [
    '2021级市场营销2023-2024学年学期第三学期 财经实习',
    '2022级软件工程2023-2024实习学年第一学期岗位实习',
    '2020级电子商务2022-2023实习学年第二学期岗位实习',
  ];

  // 用于跟踪每个报告项的展开状态
  final Map<String, bool> _expandedStates = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: '我的报告',
        backgroundColor: Colors.transparent,
        elevation: 0,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 课程头部选择器
          CourseHeaderSection(
            courseName: courseName,
            initialExpanded: false,
            availableCourses: availableCourses,
            onCourseChanged: (newCourse) {
              // 在实际应用中，这里应该根据新选择的课程重新加载数据
              setState(() {});
            },
          ),

          // 报告列表
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
              itemCount: 3, // 临时数据
              itemBuilder: (context, index) {
                final report = _createMockReport(index);
                return _buildReportItem(context, report, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建报告项
  Widget _buildReportItem(BuildContext context, BaseReport report, int index) {
    final isExpanded = _expandedStates[report.id] ?? false;

    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学生信息头部
          _buildStudentHeader(report),

          // 周报信息
          _buildReportInfo(report),

          // 问题列表
          _buildQuestionsList(report, isExpanded),

          // 底部信息
          _buildBottomInfo(report),
        ],
      ),
    );
  }

  // 构建学生信息头部
  Widget _buildStudentHeader(BaseReport report) {
    return Padding(
      padding: EdgeInsets.fromLTRB(30.w, 30.h, 30.w, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学生头像
          CircleAvatar(
            radius: 44.r,
            backgroundColor: Colors.grey[200],
            backgroundImage: const NetworkImage(AppConstants.avatar1),
          ),
          SizedBox(width: 12.w),

          // 学生信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      report.userName,
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 实习生标签
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4.r),
                        border: Border.all(color: AppTheme.blue2165f6, width: 0.5),
                      ),
                      child: Text(
                        '实习生',
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: AppTheme.blue2165f6,
                        ),
                      ),
                    ),
                    const Spacer(),
                    // 批阅状态
                    Text(
                      report.status == ReportStatus.approved ? '已批阅' : '待批阅',
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: report.status == ReportStatus.approved ? const Color(0xFF999999) : AppTheme.blue2165f6,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                // 公司名称
                Text(
                  '武汉谦通信息技术有限公司',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建周报信息
  Widget _buildReportInfo(BaseReport report) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: Text(
        '第三周周报 2025.05.15-2025.05.20',
        style: TextStyle(
          fontSize: 28.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // 构建问题列表
  Widget _buildQuestionsList(BaseReport report, bool isExpanded) {
    final questions = _getQuestions(report);

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 默认只显示第一个问题，展开后显示所有问题
          if (questions.isNotEmpty)
            _buildQuestionItem(questions[0], 1, isExpanded, report.id),

          // 展开状态下显示其余问题
          if (isExpanded && questions.length > 1)
            ...questions.skip(1).toList().asMap().entries.map((entry) {
              final index = entry.key + 2; // 从第2个问题开始
              final question = entry.value;
              return _buildQuestionItem(question, index, isExpanded, report.id);
            }).toList(),

          // 展开状态下显示批阅状态
          if (isExpanded)
            _buildReviewSection(report),
        ],
      ),
    );
  }

  // 构建单个问题项
  Widget _buildQuestionItem(Map<String, String> question, int questionNumber, bool isExpanded, String reportId) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 问题
          Text(
            '问题$questionNumber：${question['question']}',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF666666),
            ),
          ),
          SizedBox(height: 8.h),

          // 答案容器
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 30.w,vertical: 20.h),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              '答：${question['answer']}',
              style: TextStyle(
                fontSize: 24.sp,
                color: AppTheme.black999,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建批阅状态区域
  Widget _buildReviewSection(BaseReport report) {
    final isReviewed = report.status == ReportStatus.approved;

    return Container(
      child: Column(
        children: [
          if (!isReviewed) ...[
            // 待批阅状态 - 居中显示
            Center(
              child: Column(
                children: [
                  Text(
                    '暂无批阅～',
                    style: TextStyle(
                      fontSize: 28.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '请耐心等待老师的批阅',
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // 已批阅状态
            _buildReviewContent(report),
          ],
        ],
      ),
    );
  }

  // 构建已批阅内容
  Widget _buildReviewContent(BaseReport report) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 评星
        Row(
          children: [
            Text(
              '评星',
              style: TextStyle(
                fontSize: 24.sp,
                color: Colors.black87,
              ),
            ),
            SizedBox(width: 16.w),
            Row(
              children: List.generate(5, (index) {
                return Icon(
                  index < 3 ? Icons.star : (index == 3 ? Icons.star_half : Icons.star_border),
                  color: index < 4 ? Colors.amber : Colors.grey[300],
                  size: 32.sp,
                );
              }),
            ),
          ],
        ),
        SizedBox(height: 20.h),

        // 校内老师评语
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset('assets/images/teacher_comment_icon.png', width: 28.w, height: 28.h),
            SizedBox(width: 8.w),
            Text(
              '校内老师评语',
              style: TextStyle(
                fontSize: 30.sp,
                color: const Color(0xFF333333),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),

        // 老师信息和评语
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 老师头像和基本信息
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 24.r,
                  backgroundColor: Colors.grey[200],
                  backgroundImage: const NetworkImage(AppConstants.avatar2),
                ),
                SizedBox(width: 12.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '陈诚',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '2025-04-23 22:12',
                      style: TextStyle(
                        fontSize: 20.sp,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16.h),
            // 评语内容
            Text(
              '写的不错，任然有改进的位置，可以把周报写的更加详细点，可以包含你解决了什么问题，遇到了什么困难，下一步的计划，以及预期达到的目标。',
              style: TextStyle(
                fontSize: 24.sp,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 构建底部信息
  Widget _buildBottomInfo(BaseReport report) {
    final isExpanded = _expandedStates[report.id] ?? false;

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
      child: Row(
        children: [
          Text(
            '提交时间：${DateFormat('yyyy-MM-dd HH:mm').format(report.createdAt)}',
            style: TextStyle(
              fontSize: 22.sp,
              color: Colors.grey[600],
            ),
          ),
          const Spacer(),
          // 展开/收起按钮
          GestureDetector(
            onTap: () {
              setState(() {
                _expandedStates[report.id] = !isExpanded;
              });
            },
            child: Row(
              children: [
                Text(
                  isExpanded ? '收起' : '展开',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 4.w),
                Icon(
                  isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Colors.grey[600],
                  size: 24.sp,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 获取问题列表
  List<Map<String, String>> _getQuestions(BaseReport report) {
    return [
      {
        'question': '已完成用户个人中心UI优化，已全量上线？',
        'answer': '修复订单状态同步延迟BUG，测试通过率100%',
      },
      {
        'question': '已完成用户个人中心UI优化，已全量上线？',
        'answer': '修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%',
      },
      {
        'question': '已完成用户个人中心UI优化，已全量上线？',
        'answer': '修复订单状态同步延迟BUG，测试通过率100%',
      },
    ];
  }

  BaseReport _createMockReport(int index) {
    // 固定创建周报数据，因为设计图显示的是周报
    // 第一个报告为待批阅状态，第二个为已批阅状态，第三个为已提交状态
    final statuses = [ReportStatus.submitted, ReportStatus.approved, ReportStatus.submitted];

    return WeeklyReport(
      id: 'report_$index',
      status: statuses[index % 3],
      userId: 'user_1',
      userName: '李成刚',
      createdAt: DateTime.now().subtract(Duration(days: index * 7)),
      startDate: DateTime.now().subtract(Duration(days: index * 7 + 7)),
      endDate: DateTime.now().subtract(Duration(days: index * 7)),
      weekSummary: '已完成用户个人中心UI优化，已全量上线；\n修复订单状态同步延迟BUG，测试通过率100%...',
      achievements: '成功上线新功能',
      problems: '遇到了一些技术难题',
      nextWeekPlan: '计划完成剩余功能开发',
      teacherName: '陈诚',
      teacherComment: '写的不错，任然有改进的位置，可以把周报写的更加详细点，可以包含你解决了什么问题，遇到了什么困难，下一步的计划，以及预期达到的目标。',
      commentTime: DateTime.now(),
      courseName: courseName,
      title: '第三周周报',
      infoTitle: '基础信息',
      contentTitle: '周报内容',
    );
  }
}
