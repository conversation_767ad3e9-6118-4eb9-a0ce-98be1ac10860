/// -----
/// get_report_tip.dart
/// 
/// 获取报告提示词用例
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/report_tip.dart';
import '../repositories/report_tip_repository.dart';

/// 获取报告提示词用例
class GetReportTip implements UseCase<ReportTip, GetReportTipParams> {
  final ReportTipRepository _repository;

  GetReportTip(this._repository);

  @override
  Future<Either<Failure, ReportTip>> call(GetReportTipParams params) async {
    return await _repository.getReportTip(
      planId: params.planId,
      type: params.type,
    );
  }
}

/// 获取报告提示词参数
class GetReportTipParams extends Equatable {
  /// 实习计划ID
  final String planId;
  
  /// 类型（1=日报, 2=周报）
  final int type;

  const GetReportTipParams({
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, type];
}
