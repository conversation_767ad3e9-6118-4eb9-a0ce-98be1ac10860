import 'dart:convert';
import 'package:http/http.dart' as http;

Future<void> loginUser() async {
  final url = Uri.parse('http://121.196.223.94:8088/userservice/v1/user/login');

  final headers = {
    'Content-Type': 'application/json',
  };

  final body = jsonEncode({
    'password': 'abc123',
    'phone': '13777777777',
  });

  try {
    final response = await http.post(
      url,
      headers: headers,
      body: body,
    );

    if (response.statusCode == 200) {
      final jsonData = jsonDecode(response.body);
      final prettyJson = JsonEncoder.withIndent('    ').convert(jsonData);

      print('请求成功！');
      print('状态码: ${response.statusCode}');
      print('美化后的 JSON 响应:');
      print(prettyJson);
    } else {
      print('请求失败，状态码: ${response.statusCode}');
      print('响应体: ${response.body}');
    }
  } catch (e) {
    print('请求过程中发生错误: $e');
  }
}

void main() async {
  await loginUser();
}