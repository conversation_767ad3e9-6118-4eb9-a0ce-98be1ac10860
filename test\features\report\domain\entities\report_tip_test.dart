/// -----
/// report_tip_test.dart
/// 
/// 报告提示词实体测试
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/report/domain/entities/report_tip.dart';

void main() {
  group('ReportTip', () {
    test('should create ReportTip with correct properties', () {
      // arrange
      const reportTip = ReportTip(
        planId: 123,
        tip: '1.今天的主要工作是什么？\n2.您在工作中有哪些思考？',
        type: 1,
      );

      // assert
      expect(reportTip.planId, 123);
      expect(reportTip.tip, '1.今天的主要工作是什么？\n2.您在工作中有哪些思考？');
      expect(reportTip.type, 1);
    });

    test('should return correct questions from tip', () {
      // arrange
      const reportTip = ReportTip(
        planId: 123,
        tip: '1.今天的主要工作是什么？\n2.您在工作中有哪些思考？\n3.您觉得哪些可以继续优化？',
        type: 1,
      );

      // act
      final questions = reportTip.questions;

      // assert
      expect(questions, [
        '1.今天的主要工作是什么？',
        '2.您在工作中有哪些思考？',
        '3.您觉得哪些可以继续优化？',
      ]);
    });

    test('should handle empty tip', () {
      // arrange
      const reportTip = ReportTip(
        planId: 123,
        tip: '',
        type: 1,
      );

      // act
      final questions = reportTip.questions;

      // assert
      expect(questions, isEmpty);
    });

    test('should filter empty lines from tip', () {
      // arrange
      const reportTip = ReportTip(
        planId: 123,
        tip: '1.今天的主要工作是什么？\n\n2.您在工作中有哪些思考？\n   \n3.您觉得哪些可以继续优化？',
        type: 1,
      );

      // act
      final questions = reportTip.questions;

      // assert
      expect(questions, [
        '1.今天的主要工作是什么？',
        '2.您在工作中有哪些思考？',
        '3.您觉得哪些可以继续优化？',
      ]);
    });

    test('should correctly identify daily report type', () {
      // arrange
      const reportTip = ReportTip(
        planId: 123,
        tip: '日报提示',
        type: 1,
      );

      // assert
      expect(reportTip.isDailyReport, true);
      expect(reportTip.isWeeklyReport, false);
    });

    test('should correctly identify weekly report type', () {
      // arrange
      const reportTip = ReportTip(
        planId: 123,
        tip: '周报提示',
        type: 2,
      );

      // assert
      expect(reportTip.isDailyReport, false);
      expect(reportTip.isWeeklyReport, true);
    });

    test('should support equality comparison', () {
      // arrange
      const reportTip1 = ReportTip(
        planId: 123,
        tip: '测试提示',
        type: 1,
      );
      const reportTip2 = ReportTip(
        planId: 123,
        tip: '测试提示',
        type: 1,
      );
      const reportTip3 = ReportTip(
        planId: 456,
        tip: '测试提示',
        type: 1,
      );

      // assert
      expect(reportTip1, equals(reportTip2));
      expect(reportTip1, isNot(equals(reportTip3)));
    });
  });
}
