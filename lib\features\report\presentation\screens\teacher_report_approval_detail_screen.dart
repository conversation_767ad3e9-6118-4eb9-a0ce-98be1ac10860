/// -----
/// teacher_report_approval_detail_screen.dart
///
/// 教师端报告审核详情页面，用于展示学生提交的报告详情并进行批阅
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';
import 'package:flutter_demo/features/report/data/models/weekly_report.dart';
import 'package:flutter_demo/features/report/presentation/widgets/student_info_card.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class TeacherReportApprovalDetailScreen extends StatefulWidget {
  final BaseReport report;
  final ReportType reportType;

  const TeacherReportApprovalDetailScreen({
    Key? key,
    required this.report,
    required this.reportType,
  }) : super(key: key);

  @override
  State<TeacherReportApprovalDetailScreen> createState() =>
      _TeacherReportApprovalDetailScreenState();
}

class _TeacherReportApprovalDetailScreenState
    extends State<TeacherReportApprovalDetailScreen> {
  // AI评阅内容
  final String aiReviewContent = '写的不错，任然有改进的位置，可以把周报写的更加详细点，可以包含你解决了什么问题，遇到了什么困难，下一步的计划，以及预期达到的目标。';

  // 评分星级（3.5星）
  final double rating = 3.5;


  // 提交评阅
  void _submitReview() {
    // 显示绿色背景的SnackBar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('评阅已提交'),
        backgroundColor: Colors.green, // 设置绿色背景
      ),
    );
  }



  // 获取AppBar标题
  String _getAppBarTitle() {
    switch (widget.reportType) {
      case ReportType.daily:
        return '学生日报详情';
      case ReportType.weekly:
        return '学生周报详情';
      case ReportType.monthly:
        return '学生月报详情';
      case ReportType.summary:
        return '学生实习总结详情';
    }
  }

  // 获取报告周期信息
  String _getReportPeriodInfo() {
    switch (widget.reportType) {
      case ReportType.daily:
        return '日报 ${DateFormat('yyyy.MM.dd').format(
            widget.report.createdAt)}';
      case ReportType.weekly:
        final weeklyReport = widget.report as WeeklyReport;
        return '第三周周报 ${DateFormat('yyyy.MM.dd').format(
            weeklyReport.startDate)}-${DateFormat('yyyy.MM.dd').format(
            weeklyReport.endDate)}';
      case ReportType.monthly:
        return '第三月月报 2025.05.15-2025.05.20';
      case ReportType.summary:
        return '实习总结 2025.05.15-2025.05.20';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: CustomAppBar(
        title: _getAppBarTitle(),
        backgroundColor: Colors.white,
        showBackButton: true,
      ),
      // 使用bottomNavigationBar确保按钮固定在底部
      bottomNavigationBar: _buildBottomButton(),
      body: Column(
        children: [
          // 课程头部 - 使用普通文本显示
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
            color: Colors.white,
            child: Text(
              widget.report.courseName,
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 学生信息卡片
                          _buildStudentInfoCard(),

                          // 报告内容区域
                          _buildReportContentCard(),



                          // 底部安全间距
                          SizedBox(height: 16.h),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.h),
                    // AI评阅区域
                    _buildAIReviewSection(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建学生信息卡片
  Widget _buildStudentInfoCard() {
    return StudentInfoCard(
      userName: '李成儒',
      company: '武汉诚通信息技术有限公司',
      status: widget.report.status,
      position: '程序员',
      margin: EdgeInsets.zero,
      padding: EdgeInsets.all(16.r),
      avatarRadius: 88.r,
    );
  }

  // 构建报告内容区域
  Widget _buildReportContentCard() {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 报告标题
          Text(
            _getReportPeriodInfo(),
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 39.h),

          // 问题1
          _buildQuestionAnswer(
              '问题一：已完成用户个人中心UI优化，已全量上线？',
              '修复订单状态同步延迟BUG，测试通过率100%'
          ),
          SizedBox(height: 24.h),

          // 问题2
          _buildQuestionAnswer(
              '问题二：已完成用户个人中心UI优化，已全量上线？',
              '修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%'
          ),
          SizedBox(height: 24.h),

          // 问题3
          _buildQuestionAnswer(
              '问题三：已完成用户个人中心UI优化，已全量上线？',
              '修复订单状态同步延迟BUG，测试通过率100%'
          ),
          SizedBox(height: 24.h),

          // 提交时间
          Text(
            '提交时间：2025-04-23 22:12',
            style: TextStyle(
              fontSize: 24.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  // 构建问题和回答
  Widget _buildQuestionAnswer(String question, String answer) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题文本
        Text(
          question,
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.black666,
            height: 1.4,
          ),
        ),
        SizedBox(height: 16.h),

        // 回答内容 - 使用灰色背景
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 28.h),
          decoration: BoxDecoration(
            color: const Color(0xFFF7F7F7),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '答：',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black999,
                  height: 1.4,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  answer,
                  style: TextStyle(
                    fontSize: 26.sp,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建AI评阅区域
  Widget _buildAIReviewSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        image: const DecorationImage(
          image: AssetImage('assets/images/ai_review_dialog_bg.png'),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // AI机器人图标区域
          Container(
            height: 180.h,
            child: Stack(
              children: [
                Positioned(
                  left: 20.w,
                  top: 36.h,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    child: Image.asset(
                      'assets/images/ai_review_bot_icon.png',
                      width: 560.w,
                      height: 139.h,
                      alignment: Alignment.centerLeft,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 20.h),

          // 白色背景容器，包含评星区域和AI评阅内容
          Container(
            width: double.infinity,

            padding: EdgeInsets.all(20.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(bottomLeft: Radius.circular(20.r), bottomRight: Radius.circular(20.r)),
            ),
            child: Column(
              children: [
                // 评星区域
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(left: 30.w, top: 39.h, bottom: 39.h, right: 20.w),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Row(
                    children: [
                      Text(
                        '评星',
                        style: TextStyle(
                          fontSize: 26.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(width: 20.w),
                      // 星星评分
                      Row(
                        children: List.generate(5, (index) {
                          return Padding(
                            padding: EdgeInsets.only(right: index < 4 ? 40.w : 0),
                            child: index < rating.floor()
                                ? Icon(Icons.star, color: Colors.amber, size: 40.r)
                                : (index == rating.floor() && rating % 1 != 0)
                                ? Icon(Icons.star_half, color: Colors.amber, size: 40.r)
                                : Icon(Icons.star_border, color: Colors.amber, size: 40.r),
                          );
                        }),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 20.h),

                // AI评阅内容区域
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 32.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Text(
                    aiReviewContent,
                    style: TextStyle(
                      fontSize: 26.sp,
                      color: Colors.black87,
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  // 构建底部按钮
  Widget _buildBottomButton() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _submitReview,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          minimumSize: Size(double.infinity, 88.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.r),
          ),
          elevation: 0,
          padding: EdgeInsets.symmetric(vertical: 12.h),
        ),
        child: Text(
          '提交',
          style: TextStyle(
            fontSize: 30.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}