/// -----
/// report_tip.dart
/// 
/// 报告提示词实体
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 报告提示词实体
class ReportTip extends Equatable {
  /// 实习计划ID
  final int planId;
  
  /// 提示词内容
  final String tip;
  
  /// 类型（1=日报, 2=周报）
  final int type;

  const ReportTip({
    required this.planId,
    required this.tip,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, tip, type];

  /// 获取提示词问题列表
  List<String> get questions {
    if (tip.isEmpty) return [];
    
    // 按行分割提示词，过滤空行
    return tip
        .split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .toList();
  }

  /// 是否为日报类型
  bool get isDailyReport => type == 1;

  /// 是否为周报类型
  bool get isWeeklyReport => type == 2;
}
