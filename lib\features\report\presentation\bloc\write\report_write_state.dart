/// -----
/// report_write_state.dart
/// 
/// 写报告页面的状态定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';

abstract class ReportWriteState extends Equatable {
  const ReportWriteState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class ReportWriteInitial extends ReportWriteState {}

/// 加载中状态
class ReportWriteLoading extends ReportWriteState {}

/// 页面已加载状态
class ReportWriteLoaded extends ReportWriteState {
  final ReportType reportType;
  final String? reportId;
  final String selectedCourseId;
  final String selectedCourseName;
  final List<String> availableCourses;
  final String content;
  final List<AttachmentModel> attachments;
  final bool hasAIContent;
  final bool isSubmitting;
  final bool isDraft;
  final List<String>? reportTipQuestions;
  final List<String>? uploadedFileUrls;

  const ReportWriteLoaded({
    required this.reportType,
    this.reportId,
    required this.selectedCourseId,
    required this.selectedCourseName,
    required this.availableCourses,
    required this.content,
    required this.attachments,
    this.hasAIContent = false,
    this.isSubmitting = false,
    this.isDraft = false,
    this.reportTipQuestions,
    this.uploadedFileUrls,
  });

  @override
  List<Object?> get props => [
        reportType,
        reportId,
        selectedCourseId,
        selectedCourseName,
        availableCourses,
        content,
        attachments,
        hasAIContent,
        isSubmitting,
        isDraft,
        reportTipQuestions,
        uploadedFileUrls,
      ];

  ReportWriteLoaded copyWith({
    ReportType? reportType,
    String? reportId,
    String? selectedCourseId,
    String? selectedCourseName,
    List<String>? availableCourses,
    String? content,
    List<AttachmentModel>? attachments,
    bool? hasAIContent,
    bool? isSubmitting,
    bool? isDraft,
    List<String>? reportTipQuestions,
    List<String>? uploadedFileUrls,
  }) {
    return ReportWriteLoaded(
      reportType: reportType ?? this.reportType,
      reportId: reportId ?? this.reportId,
      selectedCourseId: selectedCourseId ?? this.selectedCourseId,
      selectedCourseName: selectedCourseName ?? this.selectedCourseName,
      availableCourses: availableCourses ?? this.availableCourses,
      content: content ?? this.content,
      attachments: attachments ?? this.attachments,
      hasAIContent: hasAIContent ?? this.hasAIContent,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isDraft: isDraft ?? this.isDraft,
      reportTipQuestions: reportTipQuestions ?? this.reportTipQuestions,
      uploadedFileUrls: uploadedFileUrls ?? this.uploadedFileUrls,
    );
  }
}

/// AI内容生成中状态
class ReportWriteGeneratingAI extends ReportWriteLoaded {
  const ReportWriteGeneratingAI({
    required super.reportType,
    super.reportId,
    required super.selectedCourseId,
    required super.selectedCourseName,
    required super.availableCourses,
    required super.content,
    required super.attachments,
    super.hasAIContent,
    super.isSubmitting,
    super.isDraft,
  });
}

/// 提交中状态
class ReportWriteSubmitting extends ReportWriteLoaded {
  const ReportWriteSubmitting({
    required super.reportType,
    super.reportId,
    required super.selectedCourseId,
    required super.selectedCourseName,
    required super.availableCourses,
    required super.content,
    required super.attachments,
    super.hasAIContent,
    super.isDraft,
  }) : super(isSubmitting: true);
}

/// 提交成功状态
class ReportWriteSubmitSuccess extends ReportWriteState {
  final String message;

  const ReportWriteSubmitSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// 保存草稿成功状态
class ReportWriteSaveDraftSuccess extends ReportWriteState {
  final String message;

  const ReportWriteSaveDraftSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// 错误状态
class ReportWriteError extends ReportWriteState {
  final String message;

  const ReportWriteError(this.message);

  @override
  List<Object?> get props => [message];
}

/// 历史报告加载状态
class ReportWriteHistoryLoaded extends ReportWriteState {
  final List<BaseReport> historyReports;

  const ReportWriteHistoryLoaded(this.historyReports);

  @override
  List<Object?> get props => [historyReports];
}

/// 附件上传中状态
class ReportWriteUploadingAttachment extends ReportWriteLoaded {
  final String uploadingFileName;
  final double uploadProgress;

  const ReportWriteUploadingAttachment({
    required super.reportType,
    super.reportId,
    required super.selectedCourseId,
    required super.selectedCourseName,
    required super.availableCourses,
    required super.content,
    required super.attachments,
    super.hasAIContent,
    super.isSubmitting,
    super.isDraft,
    required this.uploadingFileName,
    required this.uploadProgress,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        uploadingFileName,
        uploadProgress,
      ];
}

/// 附件上传成功状态
class ReportWriteAttachmentUploadSuccess extends ReportWriteState {
  final String message;
  final AttachmentModel attachment;

  const ReportWriteAttachmentUploadSuccess({
    required this.message,
    required this.attachment,
  });

  @override
  List<Object?> get props => [message, attachment];
}

/// 附件上传失败状态
class ReportWriteAttachmentUploadError extends ReportWriteState {
  final String message;

  const ReportWriteAttachmentUploadError(this.message);

  @override
  List<Object?> get props => [message];
}

/// 附件模型
class AttachmentModel extends Equatable {
  final String filePath;
  final String fileName;
  final String? fileSize;
  final String? serverUrl; // 服务器上传后的URL
  final bool isUploaded; // 是否已上传到服务器

  const AttachmentModel({
    required this.filePath,
    required this.fileName,
    this.fileSize,
    this.serverUrl,
    this.isUploaded = false,
  });

  AttachmentModel copyWith({
    String? filePath,
    String? fileName,
    String? fileSize,
    String? serverUrl,
    bool? isUploaded,
  }) {
    return AttachmentModel(
      filePath: filePath ?? this.filePath,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      serverUrl: serverUrl ?? this.serverUrl,
      isUploaded: isUploaded ?? this.isUploaded,
    );
  }

  @override
  List<Object?> get props => [filePath, fileName, fileSize, serverUrl, isUploaded];
}
